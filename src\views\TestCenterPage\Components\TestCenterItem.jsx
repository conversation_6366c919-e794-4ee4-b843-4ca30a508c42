import { CallCalling, Location, User } from 'iconsax-react';
import PropTypes from 'prop-types';

function TestCenterItem({ item }) {
  return (
    <div className='relative my-3'>
      {!item.active && (
        <span className='absolute left-0 top-4 z-10 rounded-r-md bg-red-200/80 px-1 py-1 text-xs text-red-600'>
          غیرفعال
        </span>
      )}
      <div
        className={`flex rounded-2xl bg-base-100 drop-shadow-sm ${!item.active ? 'grayscale' : ''}`}>
        <div className='relative basis-2/5'>
          <img
            className='z-20 aspect-square h-full w-full shrink-0 rounded-2xl rounded-bl-none rounded-tl-[67.5px] object-cover'
            src={item.image}
            alt=''
          />
        </div>
        <div className='flex basis-3/5 flex-col justify-center gap-2 p-3'>
          <h4 className='text-base font-bold text-base-content'>{item.name}</h4>
          <div className='flex flex-col gap-2'>
            <div className='flex gap-1'>
              <User className='size-4 shrink-0 text-secondary' />
              <span className='base-400 text-sm font-medium'>
                {item.manager}
              </span>
            </div>
            <div className='flex gap-1'>
              <CallCalling className='size-4 shrink-0 text-secondary' />
              <span className='base-400 text-sm font-medium'>
                {item.tel && item.mobile
                  ? `${item.mobile} - ${item.tel}`
                  : item.tel || item.mobile || ''}
              </span>
            </div>
            <div className='flex gap-1'>
              <Location className='size-4 shrink-0 text-secondary' />
              <span className='base-400 text-sm font-medium'>
                {item.address}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default TestCenterItem;

TestCenterItem.propTypes = {
  item: PropTypes.shape({
    name: PropTypes.string.isRequired,
    image: PropTypes.string.isRequired,
    manager: PropTypes.string.isRequired,
    tel: PropTypes.string,
    mobile: PropTypes.string,
    address: PropTypes.string.isRequired,
    active: PropTypes.bool,
  }).isRequired,
};
