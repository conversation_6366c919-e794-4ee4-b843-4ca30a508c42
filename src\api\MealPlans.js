import { router } from "react-query-kit";
import createFetcher from "../utils/createFetcher";

const MealPlans = router("mealPlans", {
  last: {
    detail: router.query({
      fetcher: createFetcher("/v1/front/plans/last"),
    }),
  },
  plan: {
    add: router.mutation({
      mutationFn: createFetcher("/v1/front/plans/start-plan", "POST"),
    }),
  },
  questions: {
    detail: router.query({
      fetcher: createFetcher("/v1/front/questions/online"),
    }),
    add: router.mutation({
      mutationFn: createFetcher("/v1/front/questions/online", "POST"),
    }),
  },

  tests: {
    add: router.mutation({
      mutationFn: createFetcher("/v1/front/tests", "POST"),
    }),
  },
  mealItemPerDay: router.query({
    fetcher: createFetcher("/v1/front/plans/:plans_id/day"),
  }),
  sendMealItem: router.mutation({
    mutationFn: createFetcher("/v1/front/plans/set-meal-item", "POST"),
  }),
  sendAutoMealPlan: router.mutation({
    mutationFn: createFetcher("/v1/front/plans/:id/auto-meal-plan", "POST"),
  }),
});

export default MealPlans;
