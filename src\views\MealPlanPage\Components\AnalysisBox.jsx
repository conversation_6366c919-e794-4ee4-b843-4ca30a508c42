import PropTypes from "prop-types";
import { cn } from "../../../utils/utils";

const AnalysisBox = ({
  pattern,
  title,
  description,
  buttonText,
  gradient,
  onClick,
  disabled,
}) => {
  return (
    <div
      className={cn(
        "relative flex h-40 overflow-hidden rounded-lg bg-transparent shadow-lg",
        {
          "grayscale opacity-70": disabled,
        },
      )}
    >
      {/* Colored background */}
      <div
        className={cn("absolute inset-0 h-full w-full bg-gradient-to-tr", {
          "from-primary to-primary-focus": gradient === "primary",
          "from-secondary to-secondary-focus": gradient === "secondary",
        })}
      />
      {/* White circle background for text */}
      <div className="absolute right-0 top-20 flex h-full w-1/2 items-center">
        <div className="absolute right-[-60%] top-1/2 h-[220%] w-[220%] -translate-y-1/2 rounded-full bg-base-100" />
      </div>
      {/* Text and button */}
      <div className="relative z-10 flex w-full flex-col items-start justify-center py-4 ps-8">
        <h2 className="text-right text-lg font-bold text-[#222]">{title}</h2>
        <p className="mb-3 mt-1 text-right text-base text-base-500">
          {description}
        </p>
        <button
          className={cn(
            "rounded-full bg-gradient-to-tr  max-w-md w-full  pt-2 pb-1 text-base font-bold text-base-100 shadow-sm transition-all duration-300 ",
            {
              "from-primary to-primary-focus": gradient === "primary",
              "from-secondary to-secondary-focus": gradient === "secondary",
              "cursor-not-allowed opacity-70": disabled,
            },
          )}
          onClick={disabled ? undefined : onClick}
        >
          {buttonText}
        </button>
      </div>
      {/* Pattern */}
      <div className="relative z-10 flex h-full w-40 items-center justify-center">
        <img
          src={pattern}
          alt="icon"
          className="absolute bottom-0 end-0 h-5/6"
        />
      </div>
    </div>
  );
};

AnalysisBox.propTypes = {
  pattern: PropTypes.string.isRequired,
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  buttonText: PropTypes.string.isRequired,
  gradient: PropTypes.oneOf(["primary", "secondary"]).isRequired,
  onClick: PropTypes.func.isRequired,
  hasAnalysis: PropTypes.bool,
  disabled: PropTypes.bool,
};

export default AnalysisBox;
