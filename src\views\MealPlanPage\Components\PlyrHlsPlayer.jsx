import { useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import Plyr from 'plyr';
import Hls from 'hls.js';
import 'plyr/dist/plyr.css';

export const PlyrHlsPlayer = ({ videoSrc, playerRef }) => {
  const videoRef = useRef(null);
  const hlsRef = useRef(null);

  useEffect(() => {
    if (!videoRef.current || !videoSrc) return;

    if (playerRef.current) {
      playerRef.current.destroy?.();
      playerRef.current = null;
    }

    if (hlsRef.current) {
      hlsRef.current.destroy();
      hlsRef.current = null;
    }

    const video = videoRef.current;
    const isHls = videoSrc.endsWith('.m3u8');

    const commonOptions = {
      controls: [
        'play-large',
        'play',
        'progress',
        'current-time',
        'mute',
        'volume',
        'settings',
        'fullscreen',
      ],
      settings: ['quality', 'speed'],
      ratio: '16:9',
      displayDuration: true,
      tooltips: { controls: true, seek: true },
      fullscreen: { enabled: true, fallback: true, iosNative: true },
    };

    if (isHls && Hls.isSupported()) {
      hlsRef.current = new Hls({ enableWorker: true, lowLatencyMode: true });

      hlsRef.current.loadSource(videoSrc);
      hlsRef.current.attachMedia(video);

      hlsRef.current.on(Hls.Events.MANIFEST_PARSED, () => {
        const availableQualities = hlsRef.current.levels
          .map((l) => l.height)
          .filter(Boolean)
          .sort((a, b) => b - a);

        playerRef.current = new Plyr(video, {
          ...commonOptions,
          quality: {
            default: availableQualities[0],
            options: availableQualities,
            forced: true,
            onChange: (newQuality) => {
              const levelIndex = hlsRef.current.levels.findIndex(
                (l) => l.height === newQuality,
              );
              if (levelIndex >= 0) {
                hlsRef.current.currentLevel = levelIndex;
              }
            },
          },
        });
      });

      hlsRef.current.on(Hls.Events.ERROR, (event, data) => {
        console.error('HLS error', data);
      });
    } else {
      video.src = videoSrc;
      video.onloadedmetadata = () => {
        playerRef.current = new Plyr(video, {
          ...commonOptions,
          settings: ['speed'],
        });
      };
    }

    return () => {
      playerRef.current?.destroy();
      hlsRef.current?.destroy();
    };
  }, [videoSrc, playerRef]);

  return (
    <div className='plyr__video-embed relative z-50 w-full'>
      <video
        ref={videoRef}
        playsInline
        controls
        className='plyr__video h-full w-full object-cover'
      />
    </div>
  );
};

PlyrHlsPlayer.propTypes = {
  videoSrc: PropTypes.string.isRequired,
  playerRef: PropTypes.object.isRequired,
};
