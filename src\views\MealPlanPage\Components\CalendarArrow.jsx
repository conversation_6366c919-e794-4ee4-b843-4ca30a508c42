import { useMemo } from 'react';
import { twMerge } from 'tailwind-merge';
import clsx from 'clsx';
import { ArrowLeft2, ArrowRight2 } from 'iconsax-react';
import PropTypes from 'prop-types';

const CalendarArrow = ({ side, changeDate, item, start_time, end_time }) => {
  const isDisabled = useMemo(() => {
    return !(
      item.fullDate.isSameOrAfter(start_time) &&
      item.fullDate.isSameOrBefore(end_time)
    );
  }, [start_time, end_time, item]);
  return (
    <button
      disabled={isDisabled}
      className={twMerge(
        'shadow-profileCard flex h-7 w-7 shrink-0 items-center justify-center rounded-full bg-base-100 text-primary-focus',
        clsx({
          'cursor-not-allowed bg-base-300 text-base-500': isDisabled,
          'cursor-pointer': !isDisabled,
        }),
      )}
      onClick={() => changeDate(side === 'RIGHT' ? -1 : 1)}>
      {
        {
          RIGHT: <ArrowRight2 className='h-5 w-5' />,
          LEFT: <ArrowLeft2 className='h-5 w-5' />,
        }[side]
      }
    </button>
  );
};
export default CalendarArrow;
CalendarArrow.propTypes = {
  side: PropTypes.oneOf(['LEFT', 'RIGHT']).isRequired,
  changeDate: PropTypes.func.isRequired,
  item: PropTypes.shape({
    fullDate: PropTypes.object.isRequired,
  }).isRequired,
  start_time: PropTypes.object.isRequired,
  end_time: PropTypes.object.isRequired,
};
