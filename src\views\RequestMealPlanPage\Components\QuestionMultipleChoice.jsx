import clsx from "clsx";
import PropTypes from "prop-types";
import { useCallback, useMemo } from "react";
import { twMerge } from "tailwind-merge";
export default function QuestionMultipleChoice({
  item = {},
  value = [],
  onChange,
  haveDescription = false,
}) {
  const questionAnswer = useMemo(() => {
    return value?.find((x) => +x.q_id === +item.id);
  }, [value, item]);

  const handleChangeDescription = useCallback(
    (description) => {
      if (questionAnswer) {
        onChange(
          value.reduce((acc, a) => {
            if (a.q_id === item.id) {
              acc.push({
                ...a,
                description,
              });
            } else {
              acc.push(a);
            }
            return acc;
          }, []),
        );
      } else {
        onChange([
          ...value,
          {
            q_id: item.id,
            answers: [],
            description,
          },
        ]);
      }
    },
    [value, onChange, questionAnswer, item],
  );

  const handleChangeAnswer = useCallback(
    (name, isChecked = true) => {
      if (questionAnswer) {
        onChange(
          value.reduce((acc, a) => {
            if (a.q_id === item.id) {
              acc.push({
                ...a,
                answers: isChecked
                  ? [...a.answers, name]
                  : a.answers.filter((x) => x !== name),
              });
            } else {
              acc.push(a);
            }
            return acc;
          }, []),
        );
      } else {
        onChange([
          ...value,
          {
            q_id: item.id,
            answers: [name],
          },
        ]);
      }
    },
    [value, item, questionAnswer, onChange],
  );

  return (
    <div key={item.id} className="">
      <span className="mb-2 text-sm font-bold">
        {item.title}
        {!item.optional && <span className="text-error">*</span>}
      </span>
      <div className="mt-2 flex items-center">
        <div className="flex flex-col">
          <div className="flex flex-wrap gap-2">
            {item?.answers?.map((ans) => {
              const isChecked = questionAnswer?.answers?.includes(ans.name);
              return (
                <label
                  key={ans.id}
                  className={twMerge(
                    "flex items-center gap-2 rounded-lg border border-base-300 p-1.5 pl-6 text-sm duration-300 peer-checked:border-secondary",
                    clsx({
                      "border-secondary transition-all duration-300": isChecked,
                    }),
                  )}
                >
                  <input
                    type="checkbox"
                    onChange={(e) =>
                      handleChangeAnswer(ans.name, e.target.checked)
                    }
                    checked={isChecked ?? false}
                    className="peer hidden"
                  />
                  <div className="peer-checked:outline-offset-6 flex h-3 w-3 items-center justify-center rounded-sm bg-transparent ring-1 ring-base-400 transition-all duration-300 peer-checked:bg-secondary peer-checked:text-white peer-checked:outline peer-checked:ring-2 peer-checked:ring-secondary" />
                  <span>{ans.title}</span>
                </label>
              );
            })}
          </div>
          {haveDescription && (
            <div className="mt-3">
              <span className="text-sm font-bold">سایر</span>
              <textarea
                value={questionAnswer?.description}
                onChange={(e) => handleChangeDescription(e.target.value)}
                className="textarea textarea-bordered mt-2 w-full rounded-lg text-sm text-base-600"
                placeholder="توضیحات خود را بنویسید..."
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

QuestionMultipleChoice.propTypes = {
  item: PropTypes.object.isRequired,
  onChange: PropTypes.func.isRequired,
  value: PropTypes.array.isRequired,
  haveDescription: PropTypes.bool,
};
