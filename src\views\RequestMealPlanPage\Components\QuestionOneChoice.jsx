import clsx from "clsx";
import PropTypes from "prop-types";
import { useCallback, useMemo } from "react";
import { twMerge } from "tailwind-merge";

export default function QuestionOneChoice({ item = {}, onChange, value = [] }) {
  const questionAnswer = useMemo(() => {
    return value.find((q) => +q.q_id === +item.id);
  }, [value, item]);
  const handleChangeAnswer = useCallback(
    (name) => {
      if (questionAnswer) {
        onChange(
          value.map((a) => {
            if (item.id === a.q_id) {
              return {
                ...a,
                answers:
                  item.optional && a.answers.includes(name) ? [] : [name],
              };
            }
            return a;
          }),
        );
      } else {
        onChange([
          ...value,
          {
            q_id: item.id,
            answers: [name],
          },
        ]);
      }
    },
    [value, item, questionAnswer, onChange],
  );

  return (
    <div key={item.id} className="mb-6">
      <span className="mb-2 text-sm font-bold">
        {item.title}
        {!item.optional && <span className="text-error">*</span>}
      </span>
      <div className="mt-2 flex items-center">
        <div className="flex flex-wrap gap-2">
          {item.answers.map((ans) => {
            const isChecked = questionAnswer?.answers?.includes(ans.name);
            return (
              <label
                key={ans.id}
                className={twMerge(
                  "flex items-center gap-2 rounded-lg border border-base-400 p-1.5 pl-6 text-sm",
                  clsx({
                    "border-secondary transition-all duration-300": isChecked,
                  }),
                )}
              >
                <input
                  type="checkbox"
                  name={`q${item.id}`}
                  onChange={() => handleChangeAnswer(ans.name)}
                  checked={isChecked}
                  className="radio-secondary radio checkbox-xs radio-xs border-base-400"
                />

                <span>{ans.title}</span>
              </label>
            );
          })}
        </div>
      </div>
    </div>
  );
}

QuestionOneChoice.propTypes = {
  item: PropTypes.object.isRequired,
  onChange: PropTypes.func.isRequired,
  value: PropTypes.array.isRequired,
};
