import { useQueryClient } from "@tanstack/react-query";
import { Clock, TickCircle } from "iconsax-react";
import { AnimatePresence, motion } from "motion/react";
import PropTypes from "prop-types";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import api from "../../api";
import Header from "../Components/Global/Header";

const OrderFinalizationStep = ({ next, isOnlinePlan }) => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const [remainingTime, setRemainingTime] = useState(120);
  const [pollingActive, setPollingActive] = useState(true);
  const [completedItems, setCompletedItems] = useState(new Set());
  const [currentProcessingItem, setCurrentProcessingItem] = useState(-1);
  const intervalRef = useRef(null);
  const animationTimeoutRef = useRef(null);

  const handleBack = useCallback(() => {
    navigate(-1, { replace: true });
  }, [navigate]);

  const checklistItems = [
    { text: "تحلیل وضعیت متابولیسم بدنی شما" },
    { text: "تحلیل بازه سنی، جنسیت و قد و وزن شما" },
    { text: "تحلیل درصد چربی، ماهیچه و آب بدن شما" },
    { text: "تحلیل شرایط سلامتی و بیماریهای شما" },
    { text: "تحلیل حساسیت های غذایی شما" },
    { text: "تحلیل سایر شرایط خاص شما" },
    { text: "تحلیل اهداف سلامتی شما" },
  ];

  // Timer for countdown only when isOnlinePlan is true
  useEffect(() => {
    if (isOnlinePlan && pollingActive) {
      intervalRef.current = setInterval(() => {
        setRemainingTime((prevTime) => {
          if (prevTime <= 1) {
            queryClient.invalidateQueries({
              queryKey: api.MealPlans.questions.detail.getKey(),
            });
            setPollingActive(false);
            return 0;
          }
          return prevTime - 1;
        });
      }, 1000);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [pollingActive, isOnlinePlan]);

  // Set all items as completed if not isOnlinePlan
  useEffect(() => {
    if (!isOnlinePlan) {
      const allItemsCompleted = new Set(
        Array.from({ length: checklistItems.length }, (_, i) => i),
      );
      setCompletedItems(allItemsCompleted);
      setCurrentProcessingItem(-1);
    }
  }, [isOnlinePlan, checklistItems.length]);

  // Sequential item processing animation only when isOnlinePlan is true
  useEffect(() => {
    if (!isOnlinePlan || !pollingActive) {
      if (animationTimeoutRef.current) {
        clearTimeout(animationTimeoutRef.current);
      }
      return;
    }

    const itemCount = checklistItems.length;

    const timePerItem = remainingTime / itemCount; // زمان هر آیتم

    const processItem = (index) => {
      if (index >= itemCount || !pollingActive) {
        setCurrentProcessingItem(-1);
        return;
      }

      setCurrentProcessingItem(index);

      animationTimeoutRef.current = setTimeout(() => {
        setCompletedItems((prev) => new Set([...prev, index]));
        setCurrentProcessingItem(-1);

        animationTimeoutRef.current = setTimeout(() => {
          if (pollingActive) {
            processItem(index + 1);
          }
        }, 300); // تاخیر کوتاه برای نمایش تیک
      }, timePerItem * 1000); // زمان هر آیتم به میلی‌ثانیه
    };

    animationTimeoutRef.current = setTimeout(() => {
      processItem(0);
    }, 300);

    return () => {
      if (animationTimeoutRef.current) {
        clearTimeout(animationTimeoutRef.current);
      }
    };
  }, [isOnlinePlan, pollingActive, checklistItems.length]);

  // تکمیل همه آیتم‌ها وقتی polling متوقف می‌شه (فقط برای isOnlinePlan true)
  useEffect(() => {
    if (isOnlinePlan && !pollingActive) {
      const allItemsCompleted = new Set(
        Array.from({ length: checklistItems.length }, (_, i) => i),
      );
      setCompletedItems(allItemsCompleted);
      setCurrentProcessingItem(-1);
      if (animationTimeoutRef.current) {
        clearTimeout(animationTimeoutRef.current);
      }
    }
  }, [pollingActive, isOnlinePlan, checklistItems.length]);

  const formatTime = useCallback((seconds) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  }, []);

  const buttonText = useMemo(() => {
    if (isOnlinePlan && pollingActive) {
      return `منتظر گرفتن جواب از هوش مصنوعی (${formatTime(remainingTime)})`;
    }
    // Add condition for retry
    if (isOnlinePlan && !pollingActive && remainingTime === 0) {
      return "تلاش مجدد";
    }
    return "نهایی کردن سفارش برنامه غذایی";
  }, [isOnlinePlan, pollingActive, remainingTime]);

  const handleRetry = useCallback(() => {
    setRemainingTime(120); // Reset timer to initial value
    setPollingActive(true); // Restart polling
    setCompletedItems(new Set()); // Reset completed items animation
    setCurrentProcessingItem(-1); // Reset processing item animation
    queryClient.invalidateQueries({
      queryKey: api.MealPlans.questions.detail.getKey(),
    }); // Invalidate query to refetch
  }, [queryClient]);
  return (
    <>
      <Header className="sticky top-0" back={handleBack}>
        <span className="font-semibold">اطلاعات جسمانی</span>
      </Header>

      <div className="container flex grow flex-col py-4">
        <div className="card flex grow flex-col bg-base-100">
          <div className="card-body grow gap-5 p-4">
            <div className="flex grow flex-col items-center">
              <div className="relative mt-8 grid size-32 place-items-center">
                <img
                  className="absolute inset-0 h-full w-full animate-[spin_3s_linear_infinite_reverse] object-contain"
                  src="/app/assets/images/result/result1.png"
                  alt="Background"
                />
                <img
                  className="absolute size-20 transform object-contain"
                  src="/app/assets/images/result/result2.png"
                  alt="Foreground Icon"
                />
              </div>
              <span className="my-4 px-5 text-center font-bold text-lg text-primary">
                آماده سازی برای طراحی برنامه غذایی اختصاصی شما
              </span>
              <div className="w-full space-y-3 px-4">
                {checklistItems.map((item, index) => {
                  const isCompleted = completedItems.has(index);
                  const isProcessing = currentProcessingItem === index;

                  return (
                    <div
                      key={item.text}
                      className="flex items-center gap-3 text-base-content"
                    >
                      <motion.span
                        initial={{ opacity: 0.3, scale: 0.8 }}
                        animate={{
                          opacity: isCompleted ? 1 : isProcessing ? 0.8 : 0.3,
                          scale: isCompleted ? 1 : isProcessing ? 1.1 : 0.8,
                          color: isCompleted
                            ? "oklch(var(--su))"
                            : isProcessing
                              ? "oklch(var(--wa))"
                              : "oklch(var(--bc) / 0.3)",
                        }}
                        transition={{
                          duration: 0.5,
                          scale: {
                            type: "spring",
                            stiffness: 300,
                            damping: 20,
                          },
                        }}
                      >
                        <AnimatePresence mode="wait">
                          {isCompleted ? (
                            <motion.div
                              key="tick"
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{
                                type: "spring",
                                stiffness: 500,
                                damping: 15,
                              }}
                            >
                              <TickCircle className="size-5" />
                            </motion.div>
                          ) : isProcessing ? (
                            <motion.div
                              key="clock"
                              initial={{ rotate: 0 }}
                              animate={{ rotate: 360 }}
                              transition={{
                                duration: 2,
                                repeat: Number.POSITIVE_INFINITY,
                                ease: "linear",
                              }}
                            >
                              <Clock className="size-5" />
                            </motion.div>
                          ) : (
                            <motion.div
                              key="pending"
                              initial={{ opacity: 0.3, scale: 0.8 }}
                              animate={{ opacity: 1, scale: 1 }}
                              className="size-5 rounded-full border-2 border-base-content/30"
                            />
                          )}
                        </AnimatePresence>
                      </motion.span>
                      <motion.span
                        initial={{ opacity: 0.3 }}
                        animate={{
                          opacity: isCompleted ? 1 : isProcessing ? 0.9 : 0.4,
                          color: isCompleted
                            ? "oklch(var(--bc))"
                            : isProcessing
                              ? "oklch(var(--bc))"
                              : "oklch(var(--bc) / 0.6)",
                        }}
                        transition={{ duration: 0.5 }}
                        className={`font-medium text-sm ${
                          isProcessing ? "font-semibold" : ""
                        }`}
                      >
                        {item.text}
                      </motion.span>
                    </div>
                  );
                })}
              </div>

              <div className="sticky bottom-5 mt-6 w-full pt-5 text-center">
                <button
                  type="button"
                  onClick={buttonText === "تلاش مجدد" ? handleRetry : next}
                  className="btn btn-primary w-full font-semibold text-base"
                >
                  {buttonText}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

OrderFinalizationStep.propTypes = {
  next: PropTypes.func,
  isOnlinePlan: PropTypes.bool,
};

export default OrderFinalizationStep;
