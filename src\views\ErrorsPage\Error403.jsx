import { Link } from "react-router-dom";

export default function Error403() {
  return (
    <main className="relative z-10 flex h-dvh  flex-col items-center justify-center overflow-hidden">
      <div
        className="absolute inset-0 -z-10 bg-contain bg-left bg-no-repeat opacity-20"
        style={{
          backgroundImage: "url('/assets/images/brand/logo.svg')",
        }}
      />
      <h1 className="flex items-center text-[8rem] md:text-[13rem] font-bold leading-[8rem] md:leading-[13rem]">
        403
      </h1>

      <h2 className="text-xl md:text-3xl font-bold">دسترسی غیر مجاز</h2>
      <h2 className="mt-4 text-lg md:text-2xl font-medium text-center px-4">
        شما امکان دسترسی به این صفحه را ندارید
      </h2>
      <Link
        to="/"
        className="btn btn-secondary mt-10 rounded-full px-6 md:px-10"
      >
        بازگشت به صفحه اصلی
      </Link>
    </main>
  );
}
