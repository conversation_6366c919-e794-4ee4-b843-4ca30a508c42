import { Fragment, useCallback, useEffect, useMemo, useState } from 'react';
import Day from './Day.jsx';
import SelectDay from './SelectDay.jsx';
import moment from 'jalali-moment';
import PropTypes from 'prop-types';
import CalendarArrow from './CalendarArrow.jsx';

export default function CalendarSlider({
  selectedDate,
  setSelectedDate,
  data,
  dataDays,
}) {
  const [dates, setDates] = useState(() => {});

  const generateDates = useCallback((baseDate) => {
    if (!baseDate || !moment.isMoment(baseDate)) {
      console.error('تاریخ پایه معتبر نیست');
      return [];
    }
    return [...Array(5)].map((_, i) => {
      const date = baseDate.clone().add(i - 2, 'days');
      return {
        day: date.locale('fa').format('D'),
        label: date.locale('fa').format('dddd'),
        month: date.locale('fa').format('MMMM'),
        fullDate: date,
      };
    });
  }, []);

  useEffect(() => {
    setDates(generateDates(selectedDate));
  }, [selectedDate, setDates, generateDates]);

  const startTime = useMemo(() => {
    return moment(moment(data?.plan?.start_time).format('YYYY-MM-DD'));
  }, [data]);
  const endTime = useMemo(() => {
    return moment(moment(data?.plan?.end_time).format('YYYY-MM-DD')).add(
      1,
      'd',
    );
  }, [data]);

  const changeDate = useCallback(
    (direction) => {
      setSelectedDate((prevDate) =>
        (prevDate ? prevDate.clone() : moment(dataDays?.day)).add(
          direction,
          'd',
        ),
      );
    },
    [setSelectedDate, dataDays],
  );

  return (
    <div className='mt-4 flex h-20 items-center justify-center gap-2'>
      <div className='flex w-full items-center justify-center gap-2 p-2'>
        {dates?.map((item, index) => {
          if (index === 2) {
            return (
              <SelectDay key={index} dates={item} onChange={setSelectedDate} />
            );
          }
          return (
            <Fragment key={`fragment-${index}`}>
              {index === 0 && (
                <CalendarArrow
                  changeDate={changeDate}
                  side='RIGHT'
                  start_time={startTime}
                  end_time={endTime}
                  item={dates[index + 1]}
                />
              )}
              <Day
                date={item}
                start_time={startTime}
                end_time={endTime}
                onChange={setSelectedDate}
              />
              {index === 4 && (
                <CalendarArrow
                  changeDate={changeDate}
                  side='LEFT'
                  start_time={startTime}
                  end_time={endTime}
                  item={dates[index - 1]}
                />
              )}
            </Fragment>
          );
        })}
      </div>
    </div>
  );
}

CalendarSlider.propTypes = {
  selectedDate: PropTypes.object.isRequired,
  setSelectedDate: PropTypes.func.isRequired,
  data: PropTypes.shape({
    plan: PropTypes.shape({
      start_time: PropTypes.string.isRequired,
      end_time: PropTypes.string.isRequired,
    }),
  }).isRequired,
  dataDays: PropTypes.shape({
    day: PropTypes.string.isRequired,
  }).isRequired,
};
