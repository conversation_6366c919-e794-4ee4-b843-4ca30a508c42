import PropTypes from "prop-types";

import DataItem from "./DataItem.jsx";
import MuscleAnalysisCard from "./MuscleAnalysisCard.jsx";

export default function FatAnalysisBody({ data, loading }) {
  if (loading) {
    return (
      <div className="m-6 flex flex-col items-center justify-center gap-4 pb-6">
        {[...Array(5)].map((_, index) => (
          <div key={index} className="skeleton h-20 w-full"></div>
        ))}
      </div>
    );
  }

  const muscleAnalysisItems = [
    {
      title: "وزن کل عضلات",
      name: "muscle_mass",
      icon: "body",
      unit: "کیلوگرم",
    },
    {
      title: "وزن عضلات اسکلتی",
      name: "smm",
      icon: "muscle",
      unit: "کیلوگرم",
    },
    {
      title: "وزن استخوان ها",
      name: "bone_mass",
      icon: "bone",
      unit: "کیلوگرم",
    },
    {
      title: "عضلات نیم تنه بالایی",
      name: "muscle_trunk_m",
      icon: "figure",
      unit: "کیلوگرم",
    },
  ];
  return (
    <div className="m-6 flex flex-col items-center justify-center gap-4 pb-6">
      {muscleAnalysisItems.map((item, index) => (
        <DataItem
          key={index}
          title={item.title}
          data={data}
          name={item.name}
          icon={item.icon}
          unit={item.unit}
        />
      ))}

      <div className="grid w-full grid-cols-2 gap-4">
        <MuscleAnalysisCard
          title="عضلات دست راست"
          weight={data?.test?.muscle_right_arm_m}
          unit="کیلوگرم"
          color="secondary"
          side="left"
          type="arm"
        />

        <MuscleAnalysisCard
          title="عضلات دست چپ"
          weight={data?.test?.muscle_left_arm_m}
          unit="کیلوگرم"
          color="primary"
          side="right"
          type="arm"
        />

        <MuscleAnalysisCard
          title="عضلات پا راست"
          weight={data?.test?.muscle_right_leg_m}
          unit="کیلوگرم"
          color="primary"
          side="right"
          type="foot"
        />

        <MuscleAnalysisCard
          title="عضلات پا چپ"
          weight={data?.test?.muscle_left_leg_m}
          unit="کیلوگرم"
          color="secondary"
          side="left"
          type="foot"
        />
      </div>
    </div>
  );
}

FatAnalysisBody.propTypes = {
  data: PropTypes.object,
  loading: PropTypes.bool,
};
