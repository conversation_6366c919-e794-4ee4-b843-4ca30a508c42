import { router } from "react-query-kit";
import createFetcher from "../utils/createFetcher";

const Packages = router("packages", {
  list: router.query({
    fetcher: createFetcher("/v1/front/packages/list"),
  }),
  paymentRequestOnline: router.mutation({
    mutationFn: createFetcher("/v1/front/payments/request/online", "POST"),
  }),
  paymentRequestOffline: router.mutation({
    mutationFn: createFetcher("/v1/front/payments/request", "POST"),
  }),
  paymentResult: router.mutation({
    mutationFn: createFetcher("/v1/front/payments/check", "POST"),
  }),
});

export default Packages;
