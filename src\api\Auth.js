import { router } from "react-query-kit";
import createFetcher from "../utils/createFetcher";

const Auth = router("auth", {
  sendOtp: router.mutation({
    mutationFn: createFetcher("/v1/front/user/auth", "POST"),
  }),
  verifyOtp: router.mutation({
    mutationFn: createFetcher("/v1/front/user/auth/verify", "POST"),
  }),
  me: router.query({
    fetcher: createFetcher("/v1/front/user/auth/me"),
  }),
});

export default Auth;
