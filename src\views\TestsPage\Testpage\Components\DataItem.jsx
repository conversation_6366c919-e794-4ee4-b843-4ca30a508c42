import PropTypes from "prop-types";
import { useMemo } from "react";
import { useNavigate } from "react-router-dom";
import Icon from "../../../Components/Icon.jsx";

const DataItem = ({ icon, data, title, unit, name }) => {
  const navigate = useNavigate();

  const template = useMemo(() => {
    return data?.templates?.find((item) => item.name === name);
  }, [data, name]);

  return (
    <div
      onClick={() => {
        template && navigate(name);
      }}
      className="flex w-full justify-between rounded-lg bg-base-100"
    >
      <div className="flex items-center justify-center gap-1">
        <div className="m-2 flex h-12 w-12 items-center justify-center rounded-lg bg-base-200">
          <Icon name={icon} className="h-full w-full text-secondary" />
        </div>
        <div className="flex flex-col">
          <span className="text-base font-medium text-base-400">{title}</span>
          {template && (
            <span className="text-xs text-primary-focus">مشاهده نمودار</span>
          )}
        </div>
      </div>
      <div className="clip-path flex w-1/4 flex-col items-center justify-center rounded-l-lg bg-gradient-to-b from-primary to-primary-focus pr-3 text-base-100">
        <span className="text-xl font-bold">{data?.test?.[name]}</span>
        <span className="text-sm font-normal">{unit}</span>
      </div>
    </div>
  );
};

DataItem.propTypes = {
  icon: PropTypes.string.isRequired,
  data: PropTypes.object.isRequired,
  title: PropTypes.string.isRequired,
  unit: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
};

export default DataItem;
