import { useCallback, useEffect, useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import api from "../../api";
import IntroStep from "./Components/IntroStep.jsx";
import QuestionsStep from "./Components/Questions.jsx";
import ResultStep from "./Components/ResultStep.jsx";
import OrderFinalizationStep from "./OrderFinalizationStep.jsx";
import PaymentPage from "./PaymentPage.jsx";

export default function RequestMealPlanPage() {
  const {
    data: _data,
    isLoading,
    refetch,
  } = api.Questions.checkPlanStatus.useQuery();
  const data = useMemo(() => _data?.data, [_data]);
  const [currentStep, setCurrentStep] = useState(0);

  const handleNext = useCallback(() => {
    setCurrentStep((prev) => (prev < 3 ? prev + 1 : prev));
  }, []);
  const handlePrev = useCallback(() => {
    setCurrentStep((prev) => prev - 1);
  }, []);

  const step = useMemo(() => {
    switch (currentStep) {
      case 0:
        return <IntroStep next={handleNext} />;
      case 1:
        return <QuestionsStep next={refetch} />;
      case 2:
        return <OrderFinalizationStep next={handleNext} isOnlinePlan={false} />;
      case 3:
        return <PaymentPage prev={handlePrev} />;
      case 4:
        return <ResultStep />;
      default:
        return null;
    }
  }, [currentStep, handleNext, refetch, data]);

  const navigate = useNavigate();
  useEffect(() => {
    if (data?.status === "PENDING") {
      if (data?.has_payment) {
        setCurrentStep(4);
      } else setCurrentStep(2);
    } else if (data?.status === "APPROVE") {
      navigate("/mael-plan", { replace: true });
    }
  }, [data]);
  return (
    <section className="flex min-h-dvh flex-col bg-base-200">{step}</section>
  );
}
