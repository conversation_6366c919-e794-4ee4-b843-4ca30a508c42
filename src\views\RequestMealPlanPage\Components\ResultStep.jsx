import { useCallback } from 'react';
import Header from '../../Components/Global/Header.jsx';
import { useNavigate } from 'react-router-dom';

export default function ResultStep() {
  const navigate = useNavigate();

  const handleBack = useCallback(() => {
    navigate(-1, { replace: true });
  }, [navigate]);

  return (
    <>
      <Header className='sticky top-0' back={handleBack}>
        <span className='font-semibold'>درخواست برنامه غذایی</span>
      </Header>

      <div className='container flex grow flex-col py-4'>
        <div className='card flex grow flex-col bg-base-100'>
          <div className='card-body grow gap-5 px-4 py-4'>
            <div className='flex grow flex-col items-center'>
              <img
                className='mt-16 aspect-square w-32'
                src='/app/assets/images/result/result.png'
                alt=''
              />
              <span className='my-5 text-center text-xl font-semibold text-primary-focus'>
                اطلاعات شما ثبت شد
              </span>
              <div className='rounded-2xl bg-primary/15 p-5'>
                <p className='text-justify font-light text-base-content'>
                  اطلاعات شما برای هوش مصنوعی ارسال شد تا یک برنامه شخصی برای
                  شما طراحی و ارائه نماید. برنامه شما پس از بازبینی و تایید
                  متخصصان تغذیه، تا ساعاتی دیگر ارائه خواهد شد. به محض تکمیل
                  برنامه غذایی، شما را از طریق پیامک مطلع خواهیم نمود.
                </p>
              </div>
              <div className='mt-6 text-center text-sm font-medium text-base-content'>
                خوشحالیم که در مسیر کسب سلامتی و تناسب اندام،
                <br />
                <p className='mt-1'>همراه شما هستیم</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
