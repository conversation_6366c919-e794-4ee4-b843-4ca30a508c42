import { RefreshCircle } from 'iconsax-react';
import { Button } from 'react-daisyui';
import { twMerge } from 'tailwind-merge';
import clsx from 'clsx';
import PropTypes from 'prop-types';
import MealStats from './MealStats';

const defaultImage = {
  breakfast: '/app/assets/images/icons/breakfast.png',
  lunch: '/app/assets/images/icons/lunch.png',
  dinner: '/app/assets/images/icons/dinner.png',
  morning_snack: '/app/assets/images/icons/snack.png',
  afternoon_snack: '/app/assets/images/icons/snack.png',
};
export default function MealItem({ meal, toggleState }) {
  return (
    <div className='shadow-profileCard flex flex-col items-center rounded-lg bg-base-100 p-3'>
      <div className='flex w-full items-start gap-3'>
        <div className='flex shrink-0 flex-col gap-2'>
          <img
            className={twMerge(
              'aspect-square h-20 w-20 rounded-lg',
              clsx({
                'bg-base-200 p-5 grayscale': !meal?.item?.food.image,
              }),
            )}
            src={
              meal?.item?.food.image
                ? meal?.item?.food.image
                : (defaultImage[meal?.name] ??
                  '/app/assets/images/icons/breakfast.png')
            }
            alt=''
          />

          <MealStats
            item={meal?.item?.calories}
            title={'کالری'}
            prefix={meal?.item?.calories}
            color={'accent-second'}
          />
        </div>

        <div className='flex w-full flex-col'>
          <div className='flex items-center justify-between'>
            <span className='flex flex-col items-start text-xs font-bold text-secondary'>
              {meal?.title}
              <span className='flex items-center text-sm text-base-content'>
                {meal?.item?.food.title}
              </span>
            </span>
            <Button
              onClick={toggleState}
              className='btn btn-square btn-sm w-8 bg-primary-focus text-base-100'>
              <RefreshCircle size={20} />
            </Button>
          </div>

          <div className='my-1.5 items-center gap-1 leading-4'>
            {meal?.item?.prepare.map((p, index) => (
              <div key={index}>
                <span>
                  <span className='my-2 text-xs text-base-content/80'>
                    {p.amount}
                  </span>
                  &nbsp;
                  <span className='my-2 text-xs text-base-content/80'>
                    {p.name}
                  </span>
                  &nbsp;
                  {index !== meal.item.prepare.length - 1 && (
                    <span className='text-secondary'>+</span>
                  )}
                  &nbsp;
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

MealItem.propTypes = {
  meal: PropTypes.shape({
    id: PropTypes.number.isRequired,
    calories: PropTypes.number.isRequired,
    title: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    item: PropTypes.shape({
      id: PropTypes.number.isRequired,
      food: PropTypes.shape({
        id: PropTypes.number.isRequired,
        title: PropTypes.string.isRequired,
        image: PropTypes.string,
      }),
      prepare: PropTypes.arrayOf(
        PropTypes.shape({
          component_name: PropTypes.string.isRequired,
          ingredients: PropTypes.arrayOf(
            PropTypes.shape({
              amount: PropTypes.string.isRequired,
              name: PropTypes.string.isRequired,
            }),
          ),
        }),
      ),
      protein: PropTypes.string.isRequired,
      lipid: PropTypes.string.isRequired,
      carbohydrate: PropTypes.string.isRequired,
      calories: PropTypes.string.isRequired,
    }).isRequired,
  }).isRequired,
  toggleState: PropTypes.func.isRequired,
};
