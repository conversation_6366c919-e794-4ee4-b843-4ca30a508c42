import { useModal } from './Modal.jsx';
import { Button } from 'react-daisyui';
import ModalSelectMealItem from './ModalSelectMealItem.jsx';
import MealItem from './MealItem.jsx';
import PropTypes from 'prop-types';

export default function Meal({ meal, day, reload }) {
  const { isOpen, toggleState } = useModal();
  return (
    <>
      {meal.item ? (
        <MealItem meal={meal} toggleState={toggleState} />
      ) : (
        <div className='shadow-profileCard flex items-center rounded-lg bg-base-100 p-3'>
          <div className='flex w-full items-center gap-5'>
            <img
              className='aspect-auto h-8 w-8 shrink-0'
              src={meal?.icon}
              alt='meal icon'
            />
            <div className='flex flex-col'>
              <span className='text-sm font-bold text-base-content'>
                {meal?.title}
              </span>
            </div>
          </div>
          <Button
            onClick={toggleState}
            className='btn btn-outline btn-primary btn-sm px-4'>
            انتخاب
          </Button>
        </div>
      )}
      <ModalSelectMealItem
        isOpen={isOpen}
        toggleState={toggleState}
        reload={reload}
        mealsData={meal}
        day={day}
      />
    </>
  );
}

Meal.propTypes = {
  day: PropTypes.string.isRequired,
  meal: PropTypes.shape({
    icon: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    calories: PropTypes.number.isRequired,
    item: PropTypes.shape({
      icon: PropTypes.string.isRequired,
      title: PropTypes.string.isRequired,
      calories: PropTypes.string.isRequired,
    }),
  }).isRequired,
  reload: PropTypes.func.isRequired,
};
