import PropTypes from "prop-types";
import { twMerge } from "tailwind-merge";
import { cn } from "../../../utils/utils";
import Button from "../../Components/Global/Button";

const MealPlanItem = ({
  title,
  loading,
  price,
  discountPercent,
  discountPrice,
  color,
  onClick,
}) => {
  const colors = {
    secondary: "border-secondary",
    primary: "border-primary",
  };

  return (
    <div
      className={`relative flex min-h-36 cursor-pointer flex-col items-center gap-2 rounded-2xl border bg-base-100 px-5 py-5 text-center text-base-500 ${colors[color]}`}
    >
      <div className="flex flex-col">
        <span className="line-clamp-1 text-xl font-medium">{title}</span>
      </div>
      <div className="mt-auto flex w-full flex-col items-end">
        {discountPrice && (
          <div className="flex items-center gap-1 text-sm line-through">
            <span>{discountPrice.toLocaleString("fa-IR")}</span>
            <span className="text-sm font-normal">تومان</span>
          </div>
        )}
        <div className="flex items-center gap-1 text-2xl font-bold">
          <span>{price.toLocaleString("fa-IR")}</span>
          <p className="text-xl font-normal">تومان</p>
        </div>
      </div>

      {discountPercent && (
        <div
          className={cn(
            "absolute rounded-full start-2 top-2 aspect-square p-1 flex items-center justify-center  text-lg",
            {
              " bg-primary/20  text-primary": color === "primary",
              " bg-secondary/20  text-secondary": color === "secondary",
            },
          )}
        >
          <small>%</small>
          {discountPercent}
        </div>
      )}

      <Button
        loading={loading}
        onClick={onClick}
        className={twMerge(
          color === "primary" &&
            "w-full rounded-2xl bg-gradient-to-l from-primary to-primary-focus text-base-100",
          color === "secondary" &&
            "w-full rounded-2xl bg-gradient-to-l from-secondary-focus to-secondary text-base-100",
        )}
      >
        انتخاب پلن
      </Button>
    </div>
  );
};

MealPlanItem.propTypes = {
  title: PropTypes.string.isRequired,
  loading: PropTypes.bool,
  price: PropTypes.number.isRequired,
  discountPercent: PropTypes.number,
  discountPrice: PropTypes.number,
  color: PropTypes.string.isRequired,
  onClick: PropTypes.func,
};

export default MealPlanItem;
