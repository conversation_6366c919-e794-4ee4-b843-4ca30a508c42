import { AnimatePresence, motion } from 'motion/react';
import { twMerge } from 'tailwind-merge';
import clsx from 'clsx';
import { useMemo } from 'react';
import PropTypes from 'prop-types';

export default function Day({ date, onChange, start_time, end_time }) {
  const isDisabled = useMemo(() => {
    return !(
      date.fullDate.isSameOrAfter(start_time) &&
      date.fullDate.isSameOrBefore(end_time)
    );
  }, [start_time, end_time, date]);

  return (
    <button
      className={twMerge(
        'flex w-8 shrink-0 flex-col items-center justify-between gap-2 text-base-500',
        clsx({
          'cursor-not-allowed': isDisabled,
          'cursor-pointer': !isDisabled,
        }),
      )}
      disabled={isDisabled}
      onClick={() => onChange(date?.fullDate)}>
      <AnimatePresence mode='wait'>
        <motion.span
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5, ease: 'easeInOut' }}
          className={twMerge(
            'flex h-8 w-8 shrink-0 items-center justify-center rounded-full border border-base-400',
            clsx({
              'bg-base-300': isDisabled,
            }),
          )}>
          {date.day}
        </motion.span>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5, ease: 'easeInOut' }}
          className='flex items-center'>
          <span className='text-[10px] font-normal'>{date.label}</span>
        </motion.div>
      </AnimatePresence>
    </button>
  );
}

Day.propTypes = {
  date: PropTypes.shape({
    day: PropTypes.string.isRequired,
    label: PropTypes.string.isRequired,
    month: PropTypes.string.isRequired,
    fullDate: PropTypes.object.isRequired,
  }).isRequired,
  onChange: PropTypes.func.isRequired,
  start_time: PropTypes.object.isRequired,
  end_time: PropTypes.object.isRequired,
};
