import PropTypes from "prop-types";
import { Link } from "react-router-dom";

const btnVariant = {
  primary: "btn-primary",
  secondary: "btn-secondary",
  error: "btn-error",
  success: "btn-success",
  warning: "btn-warning",
  ghost: "btn-ghost",
};
export default function Button({
  children,
  disable,
  loading = false,
  variant,
  className,
  link,
  onClick,
}) {
  return link ? (
    <Link
      disable={disable}
      to={link}
      className={`btn h-auto ${btnVariant[variant]} ${className}`}
    >
      {children}
    </Link>
  ) : (
    <button
      disabled={disable}
      onClick={loading ? undefined : onClick}
      className={`btn h-auto text-lg font-bold ${btnVariant[variant]} ${className}`}
    >
      {loading ? (
        <span className="loading loading-dots loading-md" />
      ) : (
        children
      )}
    </button>
  );
}

Button.propTypes = {
  children: PropTypes.node.isRequired,
  disable: PropTypes.bool,
  loading: PropTypes.bool,
  variant: PropTypes.string,
  className: PropTypes.string,
  link: PropTypes.string,
  onClick: PropTypes.func,
};
