import clsx from "clsx";
import { useId } from "react";

import { twMerge } from "tailwind-merge";
import "@hassanmojab/react-modern-calendar-datepicker/lib/DatePicker.css";
import PropTypes from "prop-types";

export default function FormField({
  form,
  Icon,
  SuffixIcon,
  label,
  floatingLabel = false,
  placeholder = "",
  name,
  rules,
  className,
  ...props
}) {
  const id = useId();

  return (
    <div className="form-control">
      <div className="relative">
        <input
          {...form.register(name, rules)}
          id={id}
          placeholder={placeholder}
          {...props}
          className={twMerge(
            "peer input input-bordered w-full rounded-xl py-6 transition-all duration-300 focus:border-secondary focus:outline-secondary",
            className,
            clsx({
              "pr-10": !!Icon,
            }),
          )}
        />
        {floatingLabel && (
          <label
            htmlFor={id}
            className={twMerge(
              "absolute -top-1 right-1 -translate-y-1/2 bg-base-100 px-2 text-base-content/70 transition-all peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-focus:-top-1 peer-focus:h-1/4 peer-focus:text-secondary",
              clsx({
                "right-10": !!Icon,
              }),
            )}
          >
            {label}
          </label>
        )}
        {Icon && (
          <Icon
            variant="Bold"
            className="absolute right-3.5 top-1/2 h-5 w-5 -translate-y-1/2 text-base-500 peer-focus:text-secondary"
          />
        )}
        {SuffixIcon && (
          <SuffixIcon className="absolute left-3.5 top-1/2 h-5 w-5 -translate-y-1/2 text-base-400 peer-focus:text-secondary" />
        )}
      </div>
      {form.formState.errors[name] && (
        <div className="label">
          <span className="label-text-alt">
            {form.formState.errors[name].message}
          </span>
        </div>
      )}
    </div>
  );
}

FormField.propTypes = {
  form: PropTypes.object.isRequired,
  Icon: PropTypes.elementType,
  SuffixIcon: PropTypes.elementType,
  label: PropTypes.string,
  floatingLabel: PropTypes.bool,
  placeholder: PropTypes.string,
  name: PropTypes.string.isRequired,
  rules: PropTypes.object,
  className: PropTypes.string,
};
