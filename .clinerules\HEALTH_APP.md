# Project Summary: HealthApp (تحلیل فنی پروژه)

این سند یک نمای کلی فنی از پروژه HealthApp، ساختار، اهداف و کامپوننت‌های کلیدی آن را ارائه می‌دهد. هدف این سند، فراهم آوردن درکی عمیق از معماری و پیاده‌سازی پروژه برای استفاده به عنوان یک مرجع فنی است.

## Project Overview

- **Project Name**: HealthApp (هلث اپ)
- **Description**: HealthApp یک Single Page Application (SPA) مبتنی بر React.js است که برای ارائه خدمات سلامت و رژیم غذایی طراحی شده است. این پلتفرم به کاربران امکان می‌دهد تا برنامه‌های غذایی شخصی‌سازی شده را درخواست و دریافت کنند، تست‌های سلامت مختلف را مدیریت کنند، با مراکز تست ارتباط برقرار کنند و وضعیت سلامت خود را پیگیری نمایند. معماری پروژه بر پایه کامپوننت‌های قابل استفاده مجدد، مدیریت وضعیت متمرکز و ارتباطات API RESTful استوار است.

## Key Features (قابلیت‌های اصلی)

- **Meal Plan Management**:
    - درخواست و دریافت برنامه‌های غذایی آنلاین از طریق فرم‌های تعاملی (`RequestMealPlanPage`, `RequestOnlinePlan`).
    - نمایش برنامه‌های غذایی فعلی کاربر (`MealPlanPage`).
    - مدیریت پرداخت‌ها و نتایج پرداخت (`PaymentPage`, `PaymentResultPage`).
- **Health Tests**:
    - ارائه و مدیریت تست‌های سلامت مختلف (`TestsPage`, `TestPage`).
    - مشاهده تاریخچه نتایج تست‌ها (`HistoryPage`).
- **Test Center Integration**:
    - لیست و مدیریت مراکز تست سلامت (`TestCentersPage`).
- **User Profile & Authentication**:
    - سیستم کامل احراز هویت (ثبت‌نام، ورود) با استفاده از API (`LoginPage`, `RegisterPage`).
    - مدیریت پروفایل کاربری، شامل ویرایش اطلاعات و صندوق پیام‌ها (`ProfilePage`, `EditProfilePage`, `InboxPage`, `InboxSinglePage`).
- **Global State Management**:
    - مدیریت وضعیت کاربر و تم برنامه از طریق React Context (`UserContextProvider`, `ThemeContextProvider`).
- **API Interaction**:
    - ماژولار کردن فراخوانی‌های API برای بخش‌های مختلف (Auth, MealPlans, Tests, Profile, etc.) با استفاده از `up-fetch` و `react-query-kit` برای مدیریت کش و همگام‌سازی داده‌ها.
- **Responsive & RTL UI**:
    - طراحی واکنش‌گرا با Tailwind CSS و DaisyUI.
    - پشتیبانی کامل از زبان فارسی و جهت‌گیری راست به چپ (RTL) در سراسر رابط کاربری.

## Technology Stack (پشته فناوری)

- **Frontend Core**:
    - **React.js**: کتابخانه اصلی برای ساخت رابط کاربری.
    - **Vite**: ابزار ساخت سریع (build tool) برای توسعه و تولید.
    - **React Router DOM**: برای مدیریت مسیریابی سمت کلاینت.
- **Data Management**:
    - **@tanstack/react-query (React Query)**: برای مدیریت داده‌های سمت سرور، کشینگ، همگام‌سازی و مدیریت وضعیت بارگذاری/خطا.
    - **react-query-kit**: یک لایه انتزاعی بر روی React Query برای تعریف و استفاده آسان‌تر از کوئری‌ها.
    - **up-fetch**: یک کتابخانه برای فراخوانی‌های HTTP.
- **UI/UX & Styling**:
    - **Tailwind CSS**: فریم‌ورک CSS utility-first برای استایل‌دهی سریع و قابل تنظیم.
    - **DaisyUI**: کامپوننت‌های UI مبتنی بر Tailwind CSS.
    - **PostCSS**: برای پردازش CSS.
    - **Sass**: پیش‌پردازنده CSS.
- **Form Handling**:
    - **react-hook-form**: برای مدیریت فرم‌ها و اعتبارسنجی.
- **Date & Time**:
    - **@hassanmojab/react-modern-calendar-datepicker**: انتخابگر تاریخ شمسی.
    - **jalali-moment**: برای کار با تاریخ‌های شمسی.
- **Charting & Data Visualization**:
    - **ag-charts-community**, **ag-charts-react**: برای نمودارهای پیشرفته.
    - **chart.js**, **react-chartjs-2**: برای نمودارهای عمومی.
- **Animations & Carousels**:
    - **framer-motion**, **motion**: برای انیمیشن‌های UI.
    - **embla-carousel**, **embla-carousel-react**, **react-slick**, **swiper**: برای اسلایدرها و کاروسل‌ها.
- **Notifications**:
    - **react-hot-toast**, **react-toastify**: برای نمایش پیام‌های اطلاع‌رسانی و خطا.
- **Video Playback**:
    - **plyr**, **plyr-react**, **hls.js**, **react-player**: برای پخش ویدئو و استریم HLS.
- **Code Quality & Tooling**:
    - **ESLint**: برای تحلیل استاتیک کد و شناسایی مشکلات.
    - **BiomeJS**: فرمتر و لینتر کد.
    - **Prettier**: فرمتر کد برای حفظ یکپارچگی استایل.
- **Other Utilities**:
    - **clsx**: برای ترکیب کلاس‌های CSS به صورت شرطی.
    - **localforage**: برای ذخیره‌سازی آفلاین داده‌ها.
    - **react-cookie**: برای مدیریت کوکی‌ها.
    - **uuid**: برای تولید شناسه‌های منحصر به فرد.

## Project Structure (ساختار پروژه)

پروژه از یک ساختار ماژولار پیروی می‌کند که هر بخش مسئولیت‌های مشخصی دارد:

- **`public/`**:
    - **`assets/`**: شامل فونت‌ها (`yekanBakh`), تصاویر (`images/background`, `images/banner`, `images/brand`, `images/home`, `images/icons`, `images/otp`, `images/result`, `images/tests`, `images/user`) و آیکون‌های SVG.
- **`src/`**:
    - **`App.jsx`**: کامپوننت روت برنامه، شامل `ErrorBoundary` برای مدیریت خطاهای UI و `LoadingPage` برای نمایش وضعیت بارگذاری اولیه. `Outlet` از `react-router-dom` برای رندر کردن کامپوننت‌های مسیرهای فرزند استفاده می‌شود.
    - **`main.jsx`**: نقطه ورود اصلی برنامه. شامل Providers سراسری مانند `QueryClientProvider` (برای React Query), `CookiesProvider`, `ThemeContextProvider`, و `UserContextProvider` که وضعیت سراسری را مدیریت می‌کنند.
    - **`index.css`**: فایل CSS اصلی برای استایل‌های عمومی و Tailwind.
    - **`api/`**:
        - مجموعه‌ای از ماژول‌ها (مانند `Auth.js`, `MealPlans.js`, `Tests.js`, `Profile.js`) که هر کدام مسئول ارتباط با یک بخش خاص از API بک‌اند هستند. `index.js` این ماژول‌ها را تجمیع می‌کند.
    - **`assets/`**: شامل `react.svg` و سایر دارایی‌های کوچک.
    - **`context/`**:
        - **`theme.context.jsx`**: مدیریت تم برنامه (روشن/تاریک).
        - **`user.context.jsx`**: مدیریت وضعیت احراز هویت و اطلاعات کاربر.
    - **`data/`**:
        - **`provinces.js`**: داده‌های استاتیک مربوط به استان‌ها.
        - **`upfetch.js`**: تنظیمات و نمونه‌سازی `up-fetch`.
    - **`hooks/`**:
        - **`useAppInitialization.js`**: هوک سفارشی برای مدیریت منطق بارگذاری اولیه برنامه (مانند بررسی وضعیت احراز هویت).
    - **`routers/`**:
        - **`router.jsx`**: تعریف تمام مسیرهای برنامه با استفاده از `createBrowserRouter` از `react-router-dom`. مسیرها به صورت تو در تو (nested) تعریف شده‌اند و شامل `Layout` و `HomeLayout` برای ساختاردهی صفحات هستند.
    - **`types/`**:
        - **`jsdoc.d.js`**: فایل‌های تعریف نوع (Type Definitions) برای JSDoc.
    - **`utils/`**:
        - **`createFetcher.js`**: تابع کمکی برای ایجاد نمونه‌های `up-fetch` با تنظیمات پیش‌فرض.
        - **`unit.js`**: توابع کمکی برای تبدیل واحدها.
        - **`utils.js`**: توابع کمکی عمومی.
    - **`views/`**:
        - **`AppPage/`**: کامپوننت‌های مربوط به صفحه اصلی برنامه.
        - **`Components/`**:
            - **`ErrorBoundary.jsx`**: کامپوننت برای مدیریت و نمایش خطاهای React.
            - **`Icon.jsx`**: کامپوننت برای نمایش آیکون‌ها.
            - **`Charts/`**: کامپوننت‌های نمودار.
            - **`Form/`**: کامپوننت‌های فرم (مانند `AvatarField`, `DateField`, `SelectField`).
            - **`Global/`**: کامپوننت‌های عمومی UI (مانند `Button`, `Header`, `InviteModal`).
            - **`Shop/`**: کامپوننت‌های مربوط به فروشگاه.
        - **`ContactUsPage/`**: صفحه تماس با ما.
        - **`EditProfilePage/`**: صفحه ویرایش پروفایل.
        - **`ErrorsPage/`**: صفحات خطا (403, 404, 500) و صفحه بارگذاری (`LoadingPage`).
        - **`HomeLayout.jsx`**: لایه‌بندی برای صفحات اصلی (داشبورد).
        - **`HomePage/`**: صفحه اصلی.
        - **`InboxPage/`**: صفحات صندوق پیام‌ها.
        - **`Layout.jsx`**: لایه‌بندی اصلی برنامه.
        - **`LoginPage/`**: صفحه ورود.
        - **`MealPlanPage/`**: صفحات مربوط به برنامه‌های غذایی.
        - **`OnlinePlanQuestions/`**: کامپوننت‌های مربوط به سوالات برنامه آنلاین.
        - **`ProfilePage/`**: صفحه پروفایل کاربر.
        - **`RegisterPage/`**: صفحه ثبت‌نام.
        - **`RequestMealPlanPage/`**: صفحات مربوط به درخواست برنامه غذایی.
        - **`TestCenterPage/`**: صفحات مربوط به مراکز تست.
        - **`TestsPage/`**: صفحات مربوط به تست‌های سلامت.

### Dependencies (وابستگی‌ها)

تمام وابستگی‌های پروژه در `package.json` لیست شده‌اند. این وابستگی‌ها شامل کتابخانه‌های اصلی React، ابزارهای توسعه (Vite, ESLint, BiomeJS, Prettier, Tailwind CSS), و مجموعه‌ای گسترده از کتابخانه‌های تخصصی برای UI، مدیریت داده، انیمیشن، نمودارها و پخش ویدئو هستند. مدیریت پکیج با `yarn` انجام می‌شود.

## Setup and Installation (راه‌اندازی و نصب)

### Prerequisites
- **Node.js**: نسخه 18 یا بالاتر.
- **Yarn** (توصیه می‌شود) یا **npm**.

### Installation Steps
1. **Clone the repository**:
   ```bash
   git clone [repository-url] # URL مخزن را جایگزین کنید
   cd HealthWebApp
   ```
2. **Install dependencies**:
   ```bash
   yarn install
   # or
   npm install
   ```
3. **Environment Variables**:
   یک فایل `.env` در ریشه پروژه ایجاد کنید. حداقل متغیر مورد نیاز `VITE_API_BASE_URL` است که باید به آدرس API بک‌اند اشاره کند.
   مثال:
   ```
   VITE_API_BASE_URL=http://localhost:8000/api
   ```
4. **Run the development server**:
   ```bash
   yarn dev
   # or
   npm run dev
   ```
   برنامه در `http://localhost:3005` (یا پورت مشخص شده در `package.json`) اجرا خواهد شد.

## Usage (نحوه استفاده)

### Basic Usage
پس از راه‌اندازی سرور توسعه، برنامه در مرورگر شما قابل دسترسی است. کاربران می‌توانند از طریق صفحات ورود/ثبت‌نام وارد شوند و به قابلیت‌های اصلی برنامه شامل مدیریت برنامه‌های غذایی، انجام تست‌ها، مشاهده مراکز تست و مدیریت پروفایل دسترسی پیدا کنند.

### Configuration (تنظیمات)
- **`vite.config.js`**: تنظیمات مربوط به Vite، شامل پلاگین‌های React و تنظیمات build.
- **`tailwind.config.js`**: پیکربندی Tailwind CSS، شامل مسیرهای فایل‌های تمپلیت، تم، و پلاگین‌ها.
- **`postcss.config.js`**: تنظیمات PostCSS.
- **`.eslintrc.cjs`**: قوانین ESLint برای linting کد JavaScript/JSX.
- **`biome.json`**: تنظیمات BiomeJS برای فرمت‌بندی، linting و سازماندهی کد.
- **`.prettierrc`**: قوانین Prettier برای فرمت‌بندی خودکار کد.

## Contributing (مشارکت)

### Guidelines
- **Issue Tracking**: قبل از شروع کار، یک Issue در مخزن ایجاد کنید تا تغییرات پیشنهادی مورد بحث قرار گیرد.
- **Coding Standards**: کد باید از قوانین ESLint، BiomeJS و Prettier پیروی کند. قبل از ارسال Pull Request، از اجرای دستورات lint و format اطمینان حاصل کنید.
- **Pull Request Process**: Pull Requestها باید شامل توضیحات واضحی از تغییرات، هدف آن‌ها و هرگونه تست مرتبط باشند.

## License

[Specify the project's license] (اطلاعات لایسنس در فایل‌های موجود یافت نشد.)

## Contact

- **Maintainer**: [Name/Team] (اطلاعات تماس در فایل‌های موجود یافت نشد.)
- **Email**: [Contact email] (اطلاعات تماس در فایل‌های موجود یافت نشد.)
