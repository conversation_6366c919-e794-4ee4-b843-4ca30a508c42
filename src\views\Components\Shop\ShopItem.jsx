import { Star1 } from "iconsax-react";
import PropTypes from "prop-types";
import { cn } from "./../../../utils/utils";

export default function ShopItem({ item, onClick, subscribe, className }) {
  return (
    <div
      onClick={onClick}
      className={cn("space-y-1.5 text-center text-xs", className)}
    >
      <div className="relative">
        <div
          className={cn(
            "flex aspect-square items-center justify-center overflow-hidden rounded-btn border border-base-400 p-1.5",
            {
              "border-primary": subscribe,
            },
          )}
        >
          <img src={item.image} className="w-full" alt="" />
        </div>
        <div
          className={cn(
            "invisible absolute -bottom-1.5 -left-1.5 flex h-6 w-6 items-center justify-center rounded-full bg-primary text-primary-content opacity-0 transition-all duration-300",
            {
              visible: subscribe,
              "opacity-100": subscribe,
            },
          )}
        >
          <Star1 variant="Bold" className="h-4 w-4" />
        </div>
      </div>
      <div>{item.name}</div>
    </div>
  );
}

ShopItem.propTypes = {
  item: PropTypes.object.isRequired,
  onClick: PropTypes.func.isRequired,
  subscribe: PropTypes.bool.isRequired,
  className: PropTypes.string,
};
