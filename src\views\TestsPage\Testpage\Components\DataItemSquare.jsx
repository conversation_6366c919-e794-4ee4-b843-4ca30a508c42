import { twMerge } from 'tailwind-merge';
import Icon from '../../../Components/Icon.jsx';
import { Chart21 } from 'iconsax-react';
import { useNavigate } from 'react-router-dom';
import PropTypes from 'prop-types';
const DataItemSquare = ({
  name,
  data,
  unit,
  title,
  icon,
  pattern,
  variant,
}) => {
  const navigate = useNavigate();
  return (
    <div
      onClick={() => {
        data?.templates?.find((item) => item.name === name) && navigate(name);
      }}
      className={twMerge(
        'relative aspect-square overflow-hidden rounded-3xl bg-gradient-to-bl text-base-100',
        {
          primary: 'from-primary-focus to-primary',
          secondary: 'from-secondary-focus to-secondary',
        }[variant],
      )}>
      <div className='mr-2 mt-3 flex gap-1.5'>
        <div className='flex h-10 w-10 items-center justify-center rounded-lg bg-base-200/30'>
          <Icon className='h-6 w-6 text-base-100' name={icon} />
        </div>
        <div className='flex flex-col items-start font-extrabold'>
          <span className='text-sm'>{title}</span>
          <span dir='auto'>
            {data?.test?.[name]}&nbsp;
            <span className='text-xs font-bold'>{unit}</span>
          </span>
        </div>
      </div>
      <Icon
        className='absolute bottom-0 right-0 h-14 w-14 text-base-100'
        name='quarterCircle'
      />
      <Icon className='absolute -bottom-1 left-0 h-24 w-24' name={pattern} />

      <Chart21 className='absolute bottom-2 right-2 size-7' variant='Bulk' />
    </div>
  );
};

DataItemSquare.propTypes = {
  name: PropTypes.string.isRequired,
  data: PropTypes.object.isRequired,
  unit: PropTypes.string.isRequired,
  title: PropTypes.string.isRequired,
  icon: PropTypes.string.isRequired,
  pattern: PropTypes.string.isRequired,
  variant: PropTypes.string.isRequired,
};

export default DataItemSquare;
