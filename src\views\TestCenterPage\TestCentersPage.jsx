import { useMemo } from "react";
import { Loading } from "react-daisyui";
import { useNavigate } from "react-router-dom";
import api from "../../api";
import Header from "../Components/Global/Header.jsx";
import TestCenterItem from "./Components/TestCenterItem.jsx";

export default function TestCentersPage() {
  const { data: _data, isLoading } = api.TestCenters.list.useQuery();
  const data = useMemo(() => _data?.data, [_data]);
  const navigate = useNavigate();

  return (
    <div className="h-full min-h-dvh bg-base-200 pb-6">
      <Header
        className="sticky top-0"
        back={() => {
          navigate(-1, { replace: true });
        }}
      >
        <span className="font-semibold">مراکز تست</span>
      </Header>

      {isLoading ? (
        <>
          <div className="container">
            {[1, 2, 3, 4, 5]?.map((x) => (
              <div key={x} className="skeleton my-2.5 h-36" />
            ))}
          </div>
        </>
      ) : (
        <>
          <div className="container">
            {data?.map((item) => (
              <TestCenterItem key={item.id} item={item} />
            ))}
          </div>
        </>
      )}
    </div>
  );
}
