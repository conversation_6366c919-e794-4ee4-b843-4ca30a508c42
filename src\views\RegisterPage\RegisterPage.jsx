import { Calendar, Home, Profile2User, Teacher, User } from "iconsax-react";
import moment from "jalali-moment";
import { useMemo } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import api from "../../api";
import provinces from "../../data/provinces.js";
import { isIos } from "../../utils/utils.js";
import DateSelectField from "../Components/Form/DateSelectField.jsx";
import FormField from "../Components/Form/FormField.jsx";
import SelectField from "../Components/Form/SelectField.jsx";
import Button from "../Components/Global/Button.jsx";
import Header from "../Components/Global/Header.jsx";

export default function RegisterPage() {
  const navigate = useNavigate();
  const { data: _profileData, isLoading: isProfileLoading } =
    api.Profile.detail.useQuery();
  const profileData = useMemo(() => _profileData?.data, [_profileData]);

  const { mutate: updateProfile, isLoading: isUpdateLoading } =
    api.Profile.update.useMutation();

  const form = useForm({
    values: {
      first_name: profileData?.user?.first_name,
      last_name: profileData?.user?.last_name,
    },
  });

  async function submit(data) {
    const res = await updateProfile({
      ...data,
      device_type: "IOS",
      birthday: moment(data.birthday).locale("fa").format("YYYY-MM-DD"),
    });
    if (res.status) {
      toast.success(res.message);
      if (isIos()) {
        navigate("/survey", { replace: true });
      } else {
        navigate("/invite", { replace: true });
      }
    } else {
      toast.error(res.message);
    }
  }

  const loading = useMemo(
    () => isProfileLoading || isUpdateLoading,
    [isProfileLoading, isUpdateLoading],
  );

  return (
    <section className="flex min-h-dvh flex-col">
      <Header back={() => {}} />
      <div className="container grow py-3">
        <h3 className="text-xl font-bold">اطلاعات تکمیلی</h3>
        <p className="mt-2 text-sm text-base-content/80">
          لطفا به منظور ثبت نهایی آرای خود در نظرسنجی برند محبوب، اطلاعات هویتی
          خود را تکمیل نمایید:
        </p>

        <div className="mt-5 space-y-5">
          <FormField
            Icon={User}
            form={form}
            name="first_name"
            label="نام"
            floatingLabel
          />
          <FormField
            Icon={User}
            form={form}
            name="last_name"
            label="نام خانوادگی"
            floatingLabel
          />
          <SelectField
            Icon={Teacher}
            form={form}
            name="education"
            label="تحصیلات"
            floatingLabel
            options={[
              {
                value: "UNDERDIPLOMA",
                label: "زیر دیپلم",
              },
              {
                value: "DIPLOMA",
                label: "دیپلم",
              },
              {
                value: "ASSOCIATE",
                label: "کاردانی",
              },
              {
                value: "BACHELOR",
                label: "کارشناسی",
              },
              {
                value: "MASTER",
                label: "کارشناسی ارشد",
              },
              {
                value: "DOCTORATE",
                label: "دکترا",
              },
              {
                value: "OTHER",
                label: "سایر",
              },
            ]}
          />
          <DateSelectField
            Icon={Calendar}
            form={form}
            name="birthday"
            label="تاریخ تولد"
            floatingLabel
          />

          <SelectField
            Icon={Home}
            form={form}
            name="province_id"
            label="محل سکونت"
            floatingLabel
            options={provinces?.map((x) => ({
              value: x.id?.toString(),
              label: x.name,
            }))}
          />
          <FormField
            Icon={Profile2User}
            form={form}
            name="link"
            label="کد معرف"
            floatingLabel
          />

          <div className="grid grid-cols-3 gap-3 text-sm">
            <div className="">
              <p className="mb-1 text-center">وضعیت تاهل</p>
              <div className="divide-y divide-base-300 rounded-btn border border-base-300">
                <label className="flex items-center gap-2 px-3 py-3">
                  <input
                    type="radio"
                    defaultChecked={true}
                    {...form.register("married", {})}
                    value="SINGLE"
                    className="radio-primary radio radio-xs"
                  />
                  <span>مجرد</span>
                </label>
                <label className="flex items-center gap-2 px-3 py-3">
                  <input
                    type="radio"
                    {...form.register("married", {})}
                    value="MARRIAGE"
                    className="radio-primary radio radio-xs"
                  />
                  <span>متاهل</span>
                </label>
              </div>
            </div>
            <div className="">
              <p className="mb-1 text-center">جنسیت</p>
              <div className="divide-y divide-base-300 rounded-btn border border-base-300">
                <label className="flex items-center gap-2 px-3 py-3">
                  <input
                    type="radio"
                    defaultChecked={true}
                    {...form.register("gender", {})}
                    value="MALE"
                    className="radio-primary radio radio-xs"
                  />
                  <span>مرد</span>
                </label>
                <label className="flex items-center gap-2 px-3 py-3">
                  <input
                    type="radio"
                    {...form.register("gender", {})}
                    value="FEMALE"
                    className="radio-primary radio radio-xs"
                  />
                  <span>زن</span>
                </label>
              </div>
            </div>
          </div>
        </div>
        <div className="sticky bottom-0 mt-2 bg-base-100 py-2.5">
          <Button
            loading={loading}
            onClick={form.handleSubmit(submit)}
            variant="primary"
            className="w-full"
          >
            <span>ادامه</span>
          </Button>
        </div>
      </div>
    </section>
  );
}
