import PropTypes from 'prop-types';
import DataItemSquare from './DataItemSquare.jsx';
import DataItemStatistic from './DataItemStatistic.jsx';

export default function GeneralAnalysisBody({ data, loading }) {
  if (loading) {
    return (
      <div className='m-6 flex flex-col items-center justify-center gap-4 pb-6'>
        {[...Array(5)].map((_, index) => (
          <div key={index} className='skeleton h-20 w-full'></div>
        ))}
      </div>
    );
  }
  return (
    <div className='p-5'>
      {data && (
        <div className='grid w-full grid-cols-2 divide-x-2 divide-x-reverse divide-base-300 rounded-lg bg-base-100 py-4'>
          <DataItemStatistic
            data={data}
            title='وزن شما'
            name='weight'
            unit='کیلوگرم'
            variant='primary'
          />
          <DataItemStatistic
            data={data}
            title='قد شما'
            name='height'
            unit='سانتی متر'
            variant='secondary'
          />
        </div>
      )}
      {data && (
        <div className='mt-4 grid grid-cols-2 gap-4'>
          <DataItemSquare
            variant='secondary'
            icon={'adam'}
            pattern='pattern'
            title='شاخص توده بدنی'
            unit='BMI'
            name={'bmi'}
            data={data}
          />
          <DataItemSquare
            variant='primary'
            icon={'Profile'}
            pattern='pattern2'
            title='سن متابولیک'
            unit='سال'
            name='metabolic_age'
            data={data}
          />
          <DataItemSquare
            variant='primary'
            icon={'adamArrow'}
            pattern='pattern3'
            title='متابولیسم پایه'
            unit='کیلوکالری'
            name='bmr'
            data={data}
          />
          <DataItemSquare
            variant='secondary'
            icon={'bodyWater'}
            pattern='pattern4'
            title='میزان آب بدن'
            unit='لیتر'
            name='total_water_weight'
            data={data}
          />
        </div>
      )}
    </div>
  );
}

GeneralAnalysisBody.propTypes = {
  data: PropTypes.object,
  loading: PropTypes.bool,
};
