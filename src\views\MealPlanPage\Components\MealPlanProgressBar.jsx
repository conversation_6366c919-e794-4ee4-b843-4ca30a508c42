import moment from "jalali-moment";
import PropTypes from "prop-types";
import { useMemo } from "react";

export default function MealPlanProgressBar({ data }) {
  const startTime = useMemo(() => {
    return moment(data?.plan?.start_time);
  }, [data]);
  const endTime = useMemo(() => {
    return moment(data?.plan?.end_time);
  }, [data]);
  const currentDay = useMemo(() => {
    return moment();
  }, []);
  const totalDays = useMemo(() => {
    return endTime.diff(startTime, "days");
  }, [startTime, endTime]);
  const daysPassed = useMemo(() => {
    return currentDay.diff(startTime, "days");
  }, [startTime, currentDay]);
  const progressPercentage = useMemo(() => {
    return (daysPassed / totalDays) * 100;
  }, [daysPassed, totalDays]);
  return (
    <div className="shadow-profileCard mt-4 flex w-full flex-col items-center justify-center rounded-lg bg-base-100 px-3 py-5">
      <div className="flex w-full justify-between gap-4">
        <div className="flex w-12 shrink-0 flex-col text-center">
          <span className="text-xl font-bold text-base-400">
            {data?.weight?.current}
          </span>
          <span className="whitespace-nowrap text-xs font-medium text-base-content">
            وزن فعلی
          </span>
        </div>
        <div className="flex w-full items-center">
          <span className="h-5 w-1.5 rounded-full bg-primary" />
          <div className="flex h-1 w-full items-center bg-base-300">
            <span
              className="flex h-1.5 items-center justify-end bg-primary after:h-3.5 after:w-3.5 after:shrink-0 after:-translate-x-1/2 after:rounded-full after:border-2 after:border-primary after:bg-base-100 after:ring-4 after:ring-primary/20"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
          <span className="h-5 w-1.5 rounded-full bg-base-300" />
        </div>
        <div className="flex w-12 shrink-0 flex-col text-center">
          <span className="text-xl font-bold text-base-400">
            {data?.weight?.ideal}
          </span>
          <span className="whitespace-nowrap text-xs font-medium text-base-content">
            وزن ایده آل
          </span>
        </div>
      </div>
    </div>
  );
}

MealPlanProgressBar.propTypes = {
  data: PropTypes.shape({
    plan: PropTypes.shape({
      start_time: PropTypes.string.isRequired,
      end_time: PropTypes.string.isRequired,
      total_calorie: PropTypes.number.isRequired,
    }).isRequired,
    weight: PropTypes.shape({
      current: PropTypes.number.isRequired,
      ideal: PropTypes.number.isRequired,
    }).isRequired,
  }).isRequired,
};
