if tasklist | findstr nekoray
then
    echo "Nekoray VPN is running"

  echo "Click after disabling..."
  read -n 1 -s -r -p ""
fi

BUILD_DIR="./dist/*"
REMOTE_USER="root"
REMOTE_HOST="************"
REMOTE_DIR="/home/<USER>/public_html/app"

echo "Build ..."

yarn build

echo "Start ..."

scp -P 1242 -r $BUILD_DIR $REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR

if [ $? -eq 0 ]; then
    echo "Done."
else
    echo "Error ..."
fi

echo "press any key to exit..."
read -n 1 -s -r -p ""