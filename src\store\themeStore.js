import { create } from "zustand";

const useThemeStore = create((set) => ({
  // Initial state
  title: "هلث اپ",
  breadcrumbs: [], // Default breadcrumbs
  settings: null, // Default settings

  // Actions
  setTitle: (title, breadcrumbs = []) => {
    set({ title, breadcrumbs });
    // Update document title directly here or in a useEffect hook where the store is used
    document.title = `هلث اپ - ${title}`;
  },
  setSettings: (settings) => set({ settings }),
}));

export default useThemeStore;
