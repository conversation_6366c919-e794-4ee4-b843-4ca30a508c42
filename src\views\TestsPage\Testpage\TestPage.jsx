import clsx from "clsx";
import moment from "jalali-moment";
import { useMemo, useState } from "react";
import { Loading } from "react-daisyui";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { twMerge } from "tailwind-merge";
import api from "../../../api";
import Header from "../../Components/Global/Header.jsx";
import FatAnalysisBody from "./Components/FatAnalysisBody.jsx";
import GeneralAnalysisBody from "./Components/GeneralAnalysisBody.jsx";
import MealPlanBody from "./Components/MealPlanBody.jsx";
import MuscleAnalysisBody from "./Components/MuscleAnalysisBody.jsx";

export default function TestPage() {
  const params = useParams();
  const [searchParams] = useSearchParams();
  const { data: _data, isLoading } = api.Tests.detail.useQuery({
    variables: { id: params.id },
  });
  const data = useMemo(() => _data?.data, [_data]);

  const navigate = useNavigate();
  const [currentTab, setCurrentTab] = useState(
    searchParams?.has("step") ? +searchParams?.get("step") : 2,
  );
  const test = useMemo(() => {
    return {
      1: <MuscleAnalysisBody loading={isLoading} data={data} />,
      2: <GeneralAnalysisBody loading={isLoading} data={data} />,
      3: <FatAnalysisBody loading={isLoading} data={data} />,
      4: <MealPlanBody loading={isLoading} data={data} />,
    };
  }, [data, isLoading]);

  return (
    <div className="min-h-dvh bg-base-200">
      <div className="sticky top-0 z-20 bg-base-100 shadow">
        <Header
          backClassName=""
          className="flex pb-2 pl-0 pr-4 pt-0"
          back={() => {
            navigate(-1, { replace: true });
          }}
        >
          <div className="mt-5 flex w-full justify-between">
            <div className="flex flex-col gap-1">
              {isLoading ? (
                <>
                  <div className="skeleton h-4 w-28" />
                </>
              ) : (
                <>
                  <span className="text-sm font-bold text-base-content">
                    آنالیز &nbsp;&nbsp;
                    {moment(data?.test?.test_time)
                      .locale("fa")
                      .format("YYYY/MM/DD")}
                  </span>
                </>
              )}
              {isLoading ? (
                <div className="skeleton mt-1 h-3.5 w-20" />
              ) : (
                <span className="text-xs font-medium text-base-400">
                  مرکز آنالیز : {data?.test?.test_center?.name}
                </span>
              )}
            </div>
          </div>
        </Header>
        <div role="tablist" className="bordered-primary tabs tabs-bordered">
          <div
            role="tab"
            onClick={() => setCurrentTab(1)}
            className={twMerge(
              "tab text-sm font-medium text-base-400",
              clsx({
                "tab-active !border-primary text-primary": currentTab === 1,
              }),
            )}
          >
            آنالیز چربی
          </div>
          <div
            role="tab"
            onClick={() => setCurrentTab(2)}
            className={twMerge(
              "tab text-sm font-medium text-base-400",
              clsx({
                "tab-active !border-primary text-primary": currentTab === 2,
              }),
            )}
          >
            آنالیز کلی
          </div>
          <div
            role="tab"
            onClick={() => setCurrentTab(3)}
            className={twMerge(
              "tab text-sm font-medium text-base-400",
              clsx({
                "tab-active !border-primary text-primary": currentTab === 3,
              }),
            )}
          >
            آنالیز عضلات
          </div>
          {data?.test?.meal_plan && (
            <div
              role="tab"
              onClick={() => setCurrentTab(4)}
              className={twMerge(
                "tab text-sm font-medium text-base-400",
                clsx({
                  "tab-active !border-primary text-primary": currentTab === 4,
                }),
              )}
            >
              برنامه غذایی
            </div>
          )}
        </div>
      </div>

      {test[currentTab]}
    </div>
  );
}
