import DatePicker from "@hassanmojab/react-modern-calendar-datepicker";
import moment from "jalali-moment";
import PropTypes from "prop-types";
import { Controller, useController } from "react-hook-form";
import { cn } from "../../../utils/utils";
import Icon from "../Icon";
export default function DateField({
  control,
  rules,
  placeholder = "",
  name,
  iconName,
  color = "primary",
  iconPlacement = "left",
  inputClassName,
  iconClassName,
}) {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
    rules,
  });

  const renderCustomInput = ({ ref, value }) => (
    <div className="form-control w-full">
      <div className="relative">
        <input
          {...field}
          value={
            value ? moment(value).locale("fa").format("YYYY/MM/DD") : undefined
          }
          placeholder={placeholder}
          readOnly={true}
          ref={ref}
          className={cn(
            "peer input input-bordered w-full rounded-xl py-6 transition-all duration-300",
            {
              "focus:border-primary focus:outline-primary": color === "primary",
              "focus:border-secondary focus:outline-secondary":
                color === "secondary",
              "focus:border-warning focus:outline-warning": color === "warning",
              "border-error outline-error": error,
              "focus:border-success focus:outline-success": color === "success",
              "focus:border-info focus:outline-info": color === "info",
              "pr-10": !!iconName && iconPlacement === "right",
              "pl-10": !!iconName && iconPlacement === "left",
            },
            inputClassName,
          )}
        />

        {iconName && (
          <Icon
            name={iconName}
            className={cn(
              "absolute top-1/2 h-5 w-5 -translate-y-1/2 text-base-500",
              {
                "left-3.5": iconPlacement === "left",
                "right-3.5": iconPlacement === "right",
                "peer-focus:text-primary": color === "primary",
                "peer-focus:text-secondary": color === "secondary",
                "peer-focus:text-success": color === "success",
                "peer-focus:text-warning": color === "warning",
                "text-error": error,
                "peer-focus:text-info": color === "info",
              },
              iconClassName,
            )}
          />
        )}
        {error && (
          <p className="absolute mt-2 text-sm text-error">
            {error.message || "این فیلد الزامی است."}
          </p>
        )}
      </div>
    </div>
  );

  function toObject(date) {
    return moment(date)
      .locale("fa")
      .format("YYYY/MM/DD")
      .split("/")
      .reduce((a, item, index) => {
        if (index === 0) a.year = +item;
        if (index === 1) a.month = +item;
        if (index === 2) a.day = +item;
        return a;
      }, {});
  }

  return (
    <Controller
      render={({ field: { value, onChange } }) => {
        return (
          <DatePicker
            wrapperClassName={"w-full"}
            renderInput={(args) => renderCustomInput({ ...args, value })}
            locale="fa"
            shouldHighlightWeekends
            value={value ? toObject(value) : undefined}
            onChange={(e) => {
              onChange(
                moment(
                  `${e?.year}/${e?.month?.toString()?.padStart(2, "0")}/${e?.day?.toString()?.padStart(2, "0")}`,
                  "jYYYY/jMM/jDD",
                ).format("YYYY/MM/DD"),
              );
            }}
          />
        );
      }}
      name={name}
      rules={rules}
      control={control}
    />
  );
}

DateField.propTypes = {
  control: PropTypes.object.isRequired,
  rules: PropTypes.object,
  placeholder: PropTypes.string,
  name: PropTypes.string.isRequired,
  iconName: PropTypes.node,
  iconPlacement: PropTypes.oneOf(["left", "right"]),
  iconClassName: PropTypes.string,
  inputClassName: PropTypes.string,
  color: PropTypes.oneOf([
    "primary",
    "secondary",
    "info",
    "success",
    "warning",
  ]),
};
