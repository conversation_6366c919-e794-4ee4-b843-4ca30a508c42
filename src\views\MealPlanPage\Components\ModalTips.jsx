import { InfoCircle } from 'iconsax-react';
import { useModal } from './Modal.jsx';
import { Button, Modal } from 'react-daisyui';
import PropTypes from 'prop-types';

export default function ModalTips({ tips }) {
  const { isOpen, toggleState } = useModal();
  return (
    <>
      <Button
        onClick={toggleState}
        className='btn btn-link text-sm font-bold text-secondary'>
        حتما این نکات را بخوانید!
      </Button>
      <Modal.Legacy
        responsive={true}
        open={isOpen}
        onClickBackdrop={toggleState}>
        <Button
          className='absolute left-6 top-6'
          onClick={toggleState}
          size='sm'
          color='ghost'
          shape='circle'>
          ✕
        </Button>
        <Modal.Body>
          <div className='mt-10 flex justify-center'>
            <div className='flex h-16 w-16 items-center justify-center rounded-full bg-secondary/10'>
              <InfoCircle size='48' className='text-secondary' />
            </div>
          </div>
          <p className='mt-9 font-light text-base-content'>{tips?.tips}</p>
        </Modal.Body>
        <Modal.Actions className='sticky bottom-0 mt-auto h-full w-full bg-base-100 py-2'>
          <Button
            onClick={toggleState}
            className='text-bold btn btn-primary w-full text-lg'>
            بستن
          </Button>
        </Modal.Actions>
      </Modal.Legacy>
    </>
  );
}
ModalTips.propTypes = {
  tips: PropTypes.shape({
    tips: PropTypes.string,
  }),
};
