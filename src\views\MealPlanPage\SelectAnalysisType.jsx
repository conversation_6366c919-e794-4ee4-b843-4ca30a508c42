import { ArrowLeft } from "iconsax-react";
import PropTypes from "prop-types";
import { Link, useNavigate } from "react-router-dom";
import Icon from "../Components/Icon";
import AnalysisBox from "./Components/AnalysisBox";

const SelectAnalysisType = ({ data }) => {
  const navigate = useNavigate();

  return (
    <div className="flex  flex-col gap-4 items-center  px-4 pt-8">
      <Link
        to={"/wallet"}
        className="flex w-full items-center justify-between gap-3 rounded-xl bg-base-100 p-3"
      >
        <div className="flex size-10 items-center justify-center rounded-full text-secondary">
          <Icon name={"wallet"} className="size-6 " />
        </div>
        <div className="grow text-sm font-medium text-base-content">
          کیف پول
        </div>
        <span>
          <ArrowLeft className="h-5 w-5 text-base-400" />
        </span>
      </Link>

      <div
        className="rounded-lg bg-cover bg-center aspect-[344/85] w-full bg-no-repeat"
        style={{
          backgroundImage: `url(${data?.banner})`,
        }}
      />

      <p className="text-sm text-base-500 text-center">
        برای بهبود سبک زندگی و قرار گرفتن در مسیر سلامتی همین حالا شروع کن.
        <br /> ما در این مسیر همراهت هستیم.
        <br /> برای شروع برنامه کافیه یکی از روشهای زیر رو انتخاب کنی:
      </p>

      <div className="flex w-full max-w-md flex-col gap-4">
        <AnalysisBox
          pattern="/app/assets/images/background/InPersonTestIcon.png"
          title="آنالیز حضوری"
          description="به کمک دستگاه بادی آنالیز"
          buttonText={
            data?.test && data.test?.type?.key === "PLACE"
              ? "درخواست برنامه غذایی"
              : "لیست مراکز"
          }
          gradient="primary"
          onClick={() => {
            navigate("/test-centers");
          }}
          disabled={data?.test?.type?.key === "ONLINE"}
        />

        <AnalysisBox
          pattern="/app/assets/images/background/OnlineTestIcon.png"
          title="آنالیز آنلاین"
          description="با انجام تست هوشمند"
          buttonText={
            data?.test && data.test?.type?.key === "ONLINE"
              ? "ادامه"
              : "درخواست برنامه غذایی"
          }
          gradient="secondary"
          onClick={() => {
            navigate("/request-online-plan");
          }}
          disabled={data?.test?.type?.key === "PLACE"}
        />
      </div>
    </div>
  );
};

SelectAnalysisType.propTypes = {
  data: PropTypes.object,
};

export default SelectAnalysisType;
