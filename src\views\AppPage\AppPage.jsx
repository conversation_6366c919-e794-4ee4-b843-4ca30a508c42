import { toast } from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import { Swiper, SwiperSlide } from "swiper/react";
import { getStorage, isIos } from "../../utils/utils.js";
import Button from "../Components/Global/Button.jsx";

// Import Swiper styles
import "swiper/scss";
import "swiper/scss/navigation";
import "swiper/scss/pagination";
import { useMemo } from "react";
import { Navigation, Pagination } from "swiper/modules";
import api from "../../api";

export default function AppPage() {
  const navigate = useNavigate();

  const { data: _data, isLoading } = api.Settings.detail.useQuery();
  const settings = useMemo(() => _data?.data, [_data]);

  return (
    <section className="min-h-dvh py-6">
      <div className="container flex grow flex-col items-center justify-center text-center">
        <Swiper
          className="mb-10 w-full rounded-xl"
          slidesPerView={1}
          modules={[Navigation, Pagination]}
          navigation
          pagination
        >
          <SwiperSlide>
            <img
              src="/assets/images/sliders/4.webp"
              className="w-full rounded-xl"
              alt=""
            />
          </SwiperSlide>
        </Swiper>

        <h4 className="mt-4 text-lg font-semibold">
          <span className="flex items-center gap-1 no-underline">
            {/*<Link21 className="w-6 h-6" />*/}
            <span>کمپین بزرگ به پاس همراهی</span>
          </span>
        </h4>
        <p className="mt-2">
          دوره نظرسنجی به پایان رسیده است. لذا شما می توانید با عضویت در باشگاه
          هواداری برندهای محبوبتان، از جشنواره‌ها و تخفیفات ویژه آنها مطلع شده و
          در قرعه کشی جوایز ویژه باشگاه هواداری برندها شرکت نمایید.
        </p>
        <p className="mt-5 font-semibold text-primary-focus">
          برای دریافت اپلیکیشن و عضویت در باشگاه هواداری برندها، از لینک های زیر
          استفاده کنید.
        </p>
        <div className="mt-6 grid grid-cols-1 gap-2">
          <Button
            onClick={() => {
              if (isIos()) {
                navigate("/survey");
              } else {
                getStorage().removeItem("INVITE_CODE");
                toast("این قابلیت برای دستگاه‌های iOS می‌باشد.", {
                  icon: "⚠️",
                });
              }
            }}
            variant="primary"
          >
            نسخه مخصوص دستگاه های iOS
          </Button>

          <p className="pt-4">دریافت نسخه مخصوص اندروید</p>
          <div className="grid grid-cols-3 gap-3">
            <Button
              onClick={() => {
                getStorage().removeItem("INVITE_CODE");
                window.open(
                  settings?.app?.app,
                  "_blank",
                  "noopener,noreferrer",
                );
              }}
              className="btn-outline flex flex-col items-center py-3"
              variant="primary"
            >
              <img
                src="/assets/images/icons/android.png"
                className="h-10s w-10"
                alt=""
              />
              <span className="text-xs">دریافت مستقیم</span>
            </Button>
            <Button
              onClick={() => {
                getStorage().removeItem("INVITE_CODE");
                window.open(
                  "https://myket.ir/app/com.topbrands.app",
                  "_blank",
                  "noopener,noreferrer",
                );
              }}
              className="btn-outline flex flex-col items-center py-3"
              variant="primary"
            >
              <img
                src="/assets/images/icons/myket.png"
                className="h-10s w-10"
                alt=""
              />
              <span className="text-xs">دریافت از مایکت</span>
            </Button>
            <Button
              onClick={() => {
                getStorage().removeItem("INVITE_CODE");
                window.open(
                  "https://cafebazaar.ir/app/com.topbrands.app",
                  "_blank",
                  "noopener,noreferrer",
                );
              }}
              className="btn-outline flex flex-col items-center py-3"
              variant="primary"
            >
              <img
                src="/assets/images/icons/bazaar.png"
                className="h-10s w-10"
                alt=""
              />
              <span className="text-xs">دریافت از بازار</span>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
