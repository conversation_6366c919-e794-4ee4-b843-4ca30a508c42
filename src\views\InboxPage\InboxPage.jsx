import clsx from "clsx";
import { ArrowLeft2, Message, MessageNotif } from "iconsax-react";
import moment from "jalali-moment";
import { useMemo } from "react";
import { Loading } from "react-daisyui";
import { Link, useNavigate } from "react-router-dom";
import { twMerge } from "tailwind-merge";
import api from "../../api";
import Header from "../Components/Global/Header.jsx";

export default function InboxPage() {
  const navigate = useNavigate();

  const { data: _data, isLoading } = api.Inbox.list.useQuery();
  const data = useMemo(() => _data?.data, [_data]);

  return (
    <section className="min-h-[100svh] bg-base-200">
      <Header
        back={() => {
          navigate(-1, { replace: true });
        }}
      >
        <span className="font-semibold">صندوق پیام های من</span>
      </Header>
      <div className="container space-y-4 py-5">
        {isLoading ? (
          <>
            {[1, 2, 3, 4, 5].map((x) => (
              <div className="skeleton h-28" key={x} />
            ))}
          </>
        ) : (
          <>
            {data?.map((item) => (
              <Link
                to={item.id?.toString()}
                className="card bg-base-100"
                key={item.id}
              >
                <div className="card-body flex flex-row items-start gap-2 px-3 py-3">
                  <div
                    className={twMerge(
                      "flex h-8 w-8 shrink-0 items-center justify-center bg-base-200/50 text-primary",
                      clsx({
                        "text-base-400": item.read,
                      }),
                    )}
                  >
                    {item.read ? (
                      <Message className="h-6 w-6" variant="Bold" />
                    ) : (
                      <MessageNotif className="h-6 w-6" variant="Bold" />
                    )}
                  </div>
                  <div className="grow pt-1">
                    <span className="font-semibold">{item.title}</span>
                    <p className="line-clamp-2 text-sm">{item.body}</p>
                    <div className="mt-2.5 flex w-full items-center justify-between border-t border-base-200/50 pt-1 text-xs text-base-500">
                      <span>
                        {moment().diff(moment(item.created_at), "d")
                          ? `${moment().diff(moment(item.created_at), "d")} روز پیش`
                          : "امروز"}
                      </span>
                      <span className="link-secondary inline-flex items-center gap-1">
                        <span>مشاهده </span>
                        <ArrowLeft2 className="h-4 w-4" />
                      </span>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
            {!data?.length && (
              <div className="text-center">
                هیچ پیامی برای شما ثبت نشده است.
              </div>
            )}
          </>
        )}
      </div>
    </section>
  );
}
