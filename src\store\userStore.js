import moment from "jalali-moment";
import { create } from "zustand";
import {
  getUserData,
  removeAuthToken,
  removeUserData,
  setAuthToken,
  storeUserData,
} from "../utils/utils.js";

const useUserStore = create((set) => ({
  user: getUserData(),

  login: (user, token) => {
    setAuthToken(token);
    const userWithAge = {
      ...user,
      age: moment().diff(moment(user.birthday), "year"),
    };
    storeUserData(userWithAge);
    set({ user: userWithAge });
  },
  logout: (navigate) => {
    removeUserData();
    removeAuthToken();
    set({ user: null });
    if (navigate) {
      navigate("/login");
    }
  },
  setUser: (user) => {
    const userWithAge = {
      ...user,
      age: moment().diff(moment(user.birthday), "year"),
    };
    storeUserData(userWithAge);
    set({ user: userWithAge });
  },
}));

export default useUserStore;
