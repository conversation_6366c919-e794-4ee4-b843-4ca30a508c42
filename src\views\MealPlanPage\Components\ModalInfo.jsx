import { InfoCircle } from "iconsax-react";
import PropTypes from "prop-types";
import { Button, Modal } from "react-daisyui";
import { useModal } from "./Modal.jsx";

export default function ModalInfo({ data }) {
  const { isOpen, toggleState } = useModal();
  return (
    <>
      <Button
        onClick={toggleState}
        className="shadow-profileCard btn bg-base-100 p-1 text-xs font-bold"
      >
        <InfoCircle size={24} className="text-secondary" />
        توصیه ها
      </Button>
      <Modal.Legacy
        responsive={true}
        open={isOpen}
        onClickBackdrop={toggleState}
      >
        <div className="sticky top-0 z-10 flex w-full justify-between py-3">
          <Modal.Header className="text-base-conten m-0 flex w-full items-center text-lg font-bold">
            <InfoCircle className="mb-px ml-2 mt-0.5 size-7 text-base-400" />{" "}
            توصیه ها
          </Modal.Header>
          <Button onClick={toggleState} size="sm" color="ghost" shape="circle">
            ✕
          </Button>
        </div>
        <Modal.Body>
          {data?.plan?.recommendations?.map((item, index) => (
            <div key={index} className="mt-3 flex items-start gap-1 pb-2">
              <span className="mt-1 size-3 shrink-0 rounded-full bg-primary" />
              <div className="flex flex-col">
                <span className="font-bold text-base-500">{item.title}</span>
                <span className="mt-1.5 text-base-400">{item.text}</span>
              </div>
            </div>
          ))}
        </Modal.Body>
        <Modal.Actions className="sticky bottom-0 mt-auto h-full w-full bg-base-100 py-2">
          <Button
            onClick={toggleState}
            className="text-bold btn btn-primary w-full text-lg"
          >
            بستن
          </Button>
        </Modal.Actions>
      </Modal.Legacy>
    </>
  );
}

ModalInfo.propTypes = {
  data: PropTypes.shape({
    plan: PropTypes.shape({
      recommendations: PropTypes.array,
    }),
  }).isRequired,
};
