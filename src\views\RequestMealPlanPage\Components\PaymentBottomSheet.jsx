import PropTypes from "prop-types";

const PaymentBottomSheet = ({ planTitle, planPrice, onPayment, loading }) => {
  // Static values for now - will be replaced with dynamic data later
  const walletBalance = 1200000; // Static wallet balance
  const totalAmount = planPrice * 10; // Convert back to original price
  const isWalletUsable = walletBalance >= totalAmount;
  const payableAmount = totalAmount;

  return (
    <div className="space-y-6">
      {/* Title */}
      <div className="text-center">
        <h3 className="text-lg font-semibold text-base-content">
          انتخاب پلن برنامه غذایی
        </h3>
      </div>

      {/* Payment Details Box */}
      <div className="rounded-2xl border border-base-300 p-4 space-y-4">
        {/* Total Plan Amount */}
        <div className="flex items-center justify-between">
          <span className="text-base-content">مب<PERSON>غ کل پلن:</span>
          <div className="flex items-center gap-1">
            <span className="font-semibold text-base-content">
              {totalAmount.toLocaleString("fa-IR")}
            </span>
            <span className="text-sm text-base-500">تومان</span>
          </div>
        </div>

        {/* Wallet Balance */}
        <div className="flex items-center justify-between">
          <span className="text-base-content">موجودی کیف پول شما:</span>
          {isWalletUsable ? (
            <div className="flex items-center gap-1">
              <span className="text-base-content">-</span>
              <span className="font-semibold text-base-content">
                {walletBalance.toLocaleString("fa-IR")}
              </span>
              <span className="text-sm text-base-500">تومان</span>
            </div>
          ) : (
            <span className="text-sm text-error font-medium">
              غیر قابل استفاده روی این پلن
            </span>
          )}
        </div>

        {/* Divider */}
        <div className="border-t border-base-300"></div>

        {/* Payable Amount */}
        <div className="flex items-center justify-between">
          <span className="text-base-content">قابل پرداخت:</span>
          <div className="flex items-center gap-1">
            <span className="font-bold text-lg text-base-content">
              {payableAmount.toLocaleString("fa-IR")}
            </span>
            <span className="text-base-500">تومان</span>
          </div>
        </div>
      </div>

      {/* Payment Button */}
      <button
        onClick={onPayment}
        disabled={loading}
        className="btn btn-primary w-full text-base font-semibold"
      >
        {loading ? (
          <span className="loading loading-spinner loading-sm"></span>
        ) : (
          "پرداخت"
        )}
      </button>
    </div>
  );
};

PaymentBottomSheet.propTypes = {
  planTitle: PropTypes.string.isRequired,
  planPrice: PropTypes.number.isRequired,
  onPayment: PropTypes.func.isRequired,
  loading: PropTypes.bool,
};

export default PaymentBottomSheet;
