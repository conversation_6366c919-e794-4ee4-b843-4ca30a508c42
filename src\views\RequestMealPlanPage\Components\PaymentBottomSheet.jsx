import PropTypes from "prop-types";
import { Button, Modal } from "react-daisyui";

const PaymentBottomSheet = ({
  planTitle,
  planPrice,
  onPayment,
  loading,
  isOpen,
  onClose,
}) => {
  // Static values for now - will be replaced with dynamic data later
  const walletBalance = 1200000; // Static wallet balance
  const totalAmount = planPrice * 10; // Convert back to original price
  const isWalletUsable = walletBalance >= totalAmount;
  const payableAmount = totalAmount;

  return (
    <Modal.Legacy responsive={true} open={isOpen} onClickBackdrop={onClose}>
      <div className="sticky top-0 z-10 flex w-full justify-between py-3">
        <Modal.Header className="text-base-content m-0 flex w-full items-center text-lg font-bold">
          انتخاب پلن برنامه غذایی
        </Modal.Header>
        <Button onClick={onClose} size="sm" color="ghost" shape="circle">
          ✕
        </Button>
      </div>

      <Modal.Body>
        {/* Payment Details Box */}
        <div className="rounded-2xl border border-base-300">
          {/* Total Plan Amount */}
          <div className="flex items-center p-4 justify-between">
            <span className="text-base-content">مبلغ کل پلن:</span>
            <div className="flex items-center gap-1">
              <span className="font-semibold text-base-content">
                {totalAmount.toLocaleString("fa-IR")}
              </span>
              <span className="text-sm text-base-500">تومان</span>
            </div>
          </div>

          {/* Wallet Balance */}
          <div className="flex items-center  p-4 justify-between">
            <span className="text-base-content">موجودی کیف پول شما:</span>
            {isWalletUsable ? (
              <div className="flex items-center gap-1">
                <span className="text-base-content">-</span>
                <span className="font-semibold text-base-content">
                  {walletBalance.toLocaleString("fa-IR")}
                </span>
                <span className="text-sm text-base-500">تومان</span>
              </div>
            ) : (
              <span className="text-sm text-error font-medium">
                غیر قابل استفاده روی این پلن
              </span>
            )}
          </div>

          {/* Divider */}
          <div className="border-t border-base-300"></div>

          {/* Payable Amount */}
          <div className="flex items-center  p-4 justify-between">
            <span className="text-base-content">قابل پرداخت:</span>
            <div className="flex items-center gap-1">
              <span className="font-bold text-lg text-base-content">
                {payableAmount.toLocaleString("fa-IR")}
              </span>
              <span className="text-base-500">تومان</span>
            </div>
          </div>
        </div>
      </Modal.Body>

      <Modal.Actions className="sticky bottom-0 mt-auto h-full w-full bg-base-100 py-2">
        <Button
          onClick={onPayment}
          disabled={loading}
          className="btn btn-primary w-full text-base font-semibold"
        >
          {loading ? (
            <span className="loading loading-spinner loading-sm"></span>
          ) : (
            "پرداخت"
          )}
        </Button>
      </Modal.Actions>
    </Modal.Legacy>
  );
};

PaymentBottomSheet.propTypes = {
  planTitle: PropTypes.string.isRequired,
  planPrice: PropTypes.number.isRequired,
  onPayment: PropTypes.func.isRequired,
  loading: PropTypes.bool,
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
};

export default PaymentBottomSheet;
