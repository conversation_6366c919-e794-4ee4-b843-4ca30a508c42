import PropTypes from 'prop-types';

const StartTest = ({ onClick }) => {
  return (
    <div className='container flex-1'>
      <div className='card flex flex-col bg-base-100'>
        <div className='card-body grow gap-5 px-4 py-4'>
          <div className='flex grow flex-col items-center justify-center'>
            <img
              className='mt-auto aspect-square size-2/3'
              src='/app/assets/images/background/smart-diet.png'
            />

            <h2 className='mt-10 text-lg font-semibold text-base-content'>
              رژیم هوشمند، زندگی سالم
            </h2>

            <span className='my-4 text-center text-base-500'>
              ما با استفاده از هوش مصنوعی و دانش متخصص تغذیه، رژیمی کاملاً
              شخصی‌سازی‌شده و علمی برای شما طراحی می‌کنیم. به‌جای تمرکز صرف بر
              قد و وزن، از داده‌های دقیق مانند ترکیب بدنی، سبک زندگی، رفتارهای
              غذایی و سلامت روان شما بهره می‌بریم تا به نتایج عالی دست پیدا
              کنید.
            </span>

            <button
              onClick={onClick}
              className='btn btn-primary mt-auto w-full text-lg'>
              شروع تست
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

StartTest.propTypes = {
  onClick: PropTypes.func.isRequired,
};

export default StartTest;
