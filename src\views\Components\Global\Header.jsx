import clsx from "clsx";
import { ArrowR<PERSON> } from "iconsax-react";
import PropTypes from "prop-types";
import { twMerge } from "tailwind-merge";
import Button from "./Button.jsx";

export default function Header({ back, children, className, backClassName }) {
  return (
    <header
      className={twMerge(
        "z-20 flex items-center justify-between bg-base-100 px-1 py-2",
        clsx(className),
      )}
    >
      {back && (
        <Button
          onClick={back}
          variant="ghost"
          className={twMerge(
            "btn-square outline-none focus:outline-none",
            backClassName,
          )}
        >
          <ArrowRight className="h-5 w-5" />
        </Button>
      )}
      {children}
      {back && <div className="w-12" />}
    </header>
  );
}

Header.propTypes = {
  back: PropTypes.func,
  children: PropTypes.node,
  className: PropTypes.string,
  backClassName: PropTypes.string,
};
