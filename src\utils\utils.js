import { clsx } from "clsx";
import Cookies from "js-cookie";
import { twMerge } from "tailwind-merge";

export const AUTH_TOKEN = "AUTH_TOKEN";

export const getAuthToken = () => {
  return Cookies.get(AUTH_TOKEN);
};

export const setAuthToken = (token) => {
  Cookies.set(AUTH_TOKEN, token, { path: "/" });
};

export const removeAuthToken = () => {
  Cookies.remove(AUTH_TOKEN, { path: "/" });
};

export function getStorage() {
  return window.localStorage;
}

export function storeUserData(user) {
  return getStorage().setItem("USER_DATA", JSON.stringify(user));
}

export function getUserData() {
  return getStorage().getItem("USER_DATA")
    ? JSON.parse(getStorage().getItem("USER_DATA"))
    : null;
}

export function removeUserData() {
  return getStorage().removeItem("USER_DATA");
}

export function getMobile(mobile) {
  return mobile.replace(/^\+989/, "09");
}

export function isIos() {
  return (
    [
      "iPad Simulator",
      "iPhone Simulator",
      "iPod Simulator",
      "iPad",
      "iPhone",
      "iPod",
    ].includes(navigator.platform) ||
    // iPad on iOS 13 detection
    (navigator.userAgent.includes("Mac") && "ontouchend" in document)
  );
}

export const cn = (...props) => {
  return twMerge(clsx(props));
};
