import PropTypes from "prop-types";
import { useId, useMemo } from "react";
import { Controller, useWatch } from "react-hook-form";

export default function AvatarField({ form, name, rules, defaultValue }) {
  const field = useWatch({
    control: form.control,
    name,
  });
  const preview = useMemo(() => {
    if (field) {
      return URL.createObjectURL(field);
    }
    if (field === null) {
      return "/app/assets/images/user/user.png";
    }
    return defaultValue ?? "/app/assets/images/user/user.png";
  }, [field, defaultValue]);

  const id = useId();
  return (
    <>
      <Controller
        render={({ field: { value, onChange } }) => {
          return (
            <div className="flex items-center gap-3">
              <div className="avatar shrink-0">
                <div className="mask mask-squircle w-20">
                  <img
                    src={preview ? preview : "/app/assets/images/user/user.png"}
                    alt=""
                  />
                </div>
              </div>
              <label
                htmlFor={id}
                className="btn btn-secondary btn-sm text-base-100"
              >
                انتخاب تصویر
              </label>
              {(value || (defaultValue && value !== null)) && (
                <button
                  onClick={() => onChange(null)}
                  className="btn btn-error btn-sm"
                >
                  <span>حذف تصویر</span>
                </button>
              )}
              <input
                id={id}
                onChange={(e) => {
                  onChange(e.target.files[0]);
                }}
                type="file"
                className="hidden"
              />
            </div>
          );
        }}
        name={name}
        control={form.control}
        rules={rules}
      />
      {form.formState.errors[name] && (
        <span className="block text-sm font-medium text-error">
          {form.formState.errors[name].message}
        </span>
      )}
    </>
  );
}

AvatarField.propTypes = {
  form: PropTypes.object.isRequired,
  name: PropTypes.string.isRequired,
  rules: PropTypes.object,
  defaultValue: PropTypes.string,
};
