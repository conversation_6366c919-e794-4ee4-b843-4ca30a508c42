import clsx from "clsx";
import { Arrow<PERSON>eft2, ArrowRight2 } from "iconsax-react";
import PropTypes from "prop-types";
import { useCallback, useMemo, useState } from "react";
import { Controller, useController, useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import { twMerge } from "tailwind-merge";
import api from "../../../api";
import Header from "../../Components/Global/Header.jsx";
import Step from "./Step.jsx";
import Stepper from "./Stepper.jsx";

const QuestionsStepPropTypes = {
  next: PropTypes.func.isRequired,
};

export default function QuestionsStep({ next }) {
  const { data: _data, isLoading: isQuestionsLoading } =
    api.Questions.list.useQuery();
  const data = useMemo(() => _data?.data, [_data]);

  const [currentStep, setCurrentStep] = useState(0);

  const handlePrev = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep((prev) => prev - 1);
    }
  }, [currentStep]);
  const navigate = useNavigate();

  const {
    handleSubmit,
    setError,
    control,
    formState: { errors },
  } = useForm();

  const {
    field: { value, onChange },
  } = useController({
    control,
    name: "questions",
    defaultValue: [],
  });

  const { mutate: sendQuestionsResult, isLoading: isSending } =
    api.Questions.sendResult.useMutation({
      onSuccess: (res) => {
        if (res.status) {
          toast.success(res.message);
          next();
        } else {
          if (typeof res.message === "string") {
            toast.error(res.message);
          } else {
            for (const item of res.message.validation) {
              setError(item.path, { message: item.msg });
            }
          }
        }
      },
      onError: (error) => {
        toast.error(error?.message);
      },
    });

  const submit = useCallback(
    async (data) => {
      sendQuestionsResult(data);
    },
    [sendQuestionsResult],
  );

  const stepItem = useMemo(() => {
    return data?.[currentStep];
  }, [data, currentStep]);

  const canNext = useMemo(() => {
    const requiredQuestions =
      stepItem?.questions?.filter((x) => !x.optional) || [];
    let haveError = false;
    for (const item of requiredQuestions) {
      const answer = value?.find((x) => +x.q_id === +item.id);
      if (!answer) {
        haveError = true;
      } else if (!answer.answers.length && !answer.description) {
        haveError = true;
      }
    }
    return !haveError;
  }, [value, stepItem]);

  const handleNext = useCallback(() => {
    if (!canNext) {
      toast.error("لطفا به سوالات پاسخ دهید");
      return;
    }
    if (currentStep < data.length - 1) {
      setCurrentStep((prev) => prev + 1);
    }
  }, [currentStep, data, canNext]);

  const loading = useMemo(
    () => isSending || isQuestionsLoading,
    [isSending, isQuestionsLoading],
  );

  return (
    <>
      <Header
        className="sticky top-0"
        back={() => {
          navigate(-1, { replace: true });
        }}
      >
        <span className="font-semibold"> اطلاعات جسمانی</span>
      </Header>

      <div className="container flex grow flex-col py-4">
        <div className="card flex grow flex-col bg-base-100">
          <div className="card-body grow gap-5 px-4 py-4">
            <div className="flex w-full grow flex-col">
              <Stepper currentStep={currentStep} steps={data} />
              {data && (
                <Step item={stepItem} value={value} onChange={onChange} />
              )}

              <div className="sticky bottom-5 mt-auto">
                {currentStep === data?.length - 1 && (
                  <div className="mt-auto">
                    <Controller
                      render={({ field: { value, onChange } }) => (
                        <label
                          className={twMerge(
                            "flex cursor-pointer items-center gap-2 rounded-xl px-3 py-3 duration-300",
                            clsx({
                              "border-secondary transition-all duration-300":
                                value,
                            }),
                          )}
                        >
                          <input
                            type="checkbox"
                            onChange={(e) => onChange(e.target.checked)}
                            checked={value ?? false}
                            className="peer hidden"
                          />
                          <div className="peer-checked:outline-offset-6 flex h-4 w-4 items-center justify-center rounded-sm bg-transparent ring-1 ring-base-400 transition-all duration-300 peer-checked:bg-secondary peer-checked:text-white peer-checked:outline peer-checked:ring-2 peer-checked:ring-secondary" />
                          <span>صحت مندرجات فرم بالا را تایید می نمایم</span>
                        </label>
                      )}
                      name="single_checkbox"
                      rules={{
                        required: "انتخاب این گزینه الزامی میباشد.",
                      }}
                      control={control}
                    />
                    {errors?.single_checkbox && (
                      <div className="mr-4 text-error">
                        {errors?.single_checkbox?.message}
                      </div>
                    )}
                  </div>
                )}
                <div className="mt-2 flex flex-col justify-between gap-5">
                  <div className="flex w-full items-center gap-3">
                    {currentStep > 0 && (
                      <button
                        onClick={handlePrev}
                        className="text-bold btn btn-secondary h-14 w-14 text-lg"
                      >
                        <ArrowRight2 className="h-6 w-6" />
                      </button>
                    )}
                    {currentStep < data?.length - 1 ? (
                      <button
                        onClick={handleNext}
                        className={twMerge(
                          "text-bold btn btn-primary grow text-lg",
                          clsx({
                            "col-span-2 w-full": currentStep === 0,
                          }),
                        )}
                      >
                        مرحله بعدی
                        <ArrowLeft2 className="h-6 w-6" />
                      </button>
                    ) : (
                      <button
                        onClick={handleSubmit(submit)}
                        className="text-bold btn btn-primary grow text-lg"
                        disabled={loading}
                      >
                        ثبت اطلاعات
                        <ArrowLeft2 className="h-6 w-6" />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

QuestionsStep.propTypes = QuestionsStepPropTypes;
