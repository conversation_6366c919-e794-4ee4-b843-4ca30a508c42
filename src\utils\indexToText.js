function indexToText(number) {
  const numbers = [
    "صفر",
    "اول",
    "دوم",
    "سوم",
    "چهارم",
    "پنجم",
    "ششم",
    "هفتم",
    "هشتم",
    "نهم",
    "دهم",
    "یازدهم",
    "دوازدهم",
    "سیزدهم",
    "چهاردهم",
    "پانزدهم",
    "شانزدهم",
    "هفدهم",
    "هجدهم",
    "نوزدهم",
  ];
  const tens = [
    "",
    "",
    "بیست",
    "سی",
    "چهل",
    "پنجاه",
    "شصت",
    "هفتاد",
    "هشتاد",
    "نود",
  ];
  const hundreds = [
    "",
    "صد",
    "دویست",
    "سیصد",
    "چهارصد",
    "پانصد",
    "ششصد",
    "هفتصد",
    "هشتصد",
    "نهصد",
  ];

  if (number === 0) {
    return numbers[0];
  }

  let text = "";
  let remaining = number;

  if (remaining >= 1000) {
    text += `${numbers[Math.floor(remaining / 1000)]} هزار${remaining === 100 ? "م" : ""} `;
    remaining %= 1000;
  }

  if (remaining >= 100) {
    text +=
      hundreds[Math.floor(remaining / 100)] + (remaining === 100 ? "م" : " ");
    remaining %= 100;
  }

  if (remaining >= 20) {
    text += tens[Math.floor(remaining / 10)] + (!(remaining % 10) ? "م" : " ");
    remaining %= 10;
  }

  if (remaining > 0) {
    text += text && remaining === 1 ? "یکم" : numbers[remaining];
  }

  return text.trim();
}

export default indexToText;
