import Header from '../Components/Global/Header.jsx';
import { useNavigate } from 'react-router-dom';
import { Call, Location, Messages3 } from 'iconsax-react';

export default function ContactUsPage() {
  const navigate = useNavigate();
  return (
    <section className='min-h-[100svh] bg-base-200'>
      <Header
        back={() => {
          navigate(-1, { replace: true });
        }}>
        <span className='font-semibold'> ارتباط با ما </span>
      </Header>
      <div className='container space-y-4 py-5'>
        <div className='card flex grow flex-col items-center gap-5 bg-base-100 p-6'>
          <p className='text-base font-normal'>
            با روش های زیر می توانید با ما در ارتباط باشید.
          </p>
          <div className='flex h-14 w-14 items-center justify-center rounded-full bg-base-400/30'>
            <Location className='h-8 w-8 text-secondary' />
          </div>
          <p className='text-center text-base font-normal'>
            استان تهران، شهرستان تهران، بخش مرکزی، شهر تهران، تیموری، خیابان
            شهید احمد قاسمی، کوچه گلستان، پلاک 7
          </p>
          <p className='text-center text-base font-normal'>
            تهران، مرکز نوآوری دانشگاه شریف، طبقه 7 واحد 715
          </p>
          <div className='flex h-14 w-14 items-center justify-center rounded-full bg-base-400/30'>
            <Call className='h-8 w-8 text-secondary' />
          </div>
          <p className='text-base font-normal'>02166072596</p>
          <div className='flex h-14 w-14 items-center justify-center rounded-full bg-base-400/30'>
            <Messages3 className='h-8 w-8 text-secondary' />
          </div>
          <p className='text-base font-normal'><EMAIL></p>
        </div>
      </div>
    </section>
  );
}
