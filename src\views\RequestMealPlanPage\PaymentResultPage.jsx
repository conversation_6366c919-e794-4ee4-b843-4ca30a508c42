import { useEffect, useMemo } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import api from "../../api";

const PaymentResultPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const {
    mutate: checkPaymentStatus,
    isLoading,
    data: _data,
  } = api.Packages.paymentResult.useMutation({
    onSuccess: (res) => {
      if (res?.data?.payment?.status === "SUCCESS") {
        navigate("/request-step", { replace: true });
      }
    },
    onError: (error) => {
      console.error("Payment check failed:", error);
    },
  });

  const data = useMemo(() => _data?.data, [_data]);

  useEffect(() => {
    const authority = searchParams.get("authority");
    if (authority) {
      checkPaymentStatus({ authority });
    }
  }, [searchParams]);

  if (isLoading || !data) {
    return (
      <div className="flex h-dvh flex-col items-center justify-center">
        <div className="loading loading-spinner loading-lg text-primary" />
      </div>
    );
  }

  if (data?.payment?.status !== "SUCCESS") {
    return (
      <div className="flex h-dvh flex-col p-8 lg:max-w-96">
        <div className="flex grow flex-col items-center justify-center text-center">
          <img
            src="/app/assets/images/result/failed-result.png"
            alt="Payment Failed"
          />{" "}
          <h1 className={"mt-8 line-clamp-1 text-2xl font-bold text-secondary"}>
            پرداخت انجام نشد
          </h1>
          <h2 className="mt-2 line-clamp-3 text-sm text-base-600">
            لطفا دوباره تلاش کنید
          </h2>
        </div>

        <button
          onClick={() => {
            navigate("/request-online-plan", { replace: true });
          }}
          className="btn btn-primary mt-auto"
        >
          بازگشت
        </button>
      </div>
    );
  }

  return null;
};

export default PaymentResultPage;
