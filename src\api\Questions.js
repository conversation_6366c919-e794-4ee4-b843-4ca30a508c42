import { router } from "react-query-kit";
import createFetcher from "../utils/createFetcher";

const Questions = router("questions", {
  list: router.query({
    fetcher: createFetcher("/v1/front/questions"),
  }),
  sendResult: router.mutation({
    mutationFn: createFetcher("/v1/front/questions", "POST"),
  }),
  checkPlanStatus: router.query({
    fetcher: createFetcher("/v1/front/questions/check-plan-status"),
  }),
});

export default Questions;
