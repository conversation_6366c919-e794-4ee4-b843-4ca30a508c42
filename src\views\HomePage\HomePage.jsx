import { Calendar } from "iconsax-react";
import moment from "jalali-moment";
import { useMemo } from "react";
import toast from "react-hot-toast";
import { Link, useNavigate } from "react-router-dom";
import api from "../../api";
import useUserStore from "../../store/userStore.js"; // Import Zustand user store
import Icon from "../Components/Icon.jsx";
import { TestsListPlaceholder } from "../TestsPage/TestsPage.jsx";

export default function HomePage() {
  const user = useUserStore((state) => state.user);
  const { data: _data, isLoading } = api.Home.detail.useQuery();
  const data = useMemo(() => _data?.data, [_data]);

  const navigate = useNavigate();

  function handleUserFeature() {
    if (user?.is_feature) {
      navigate("/request");
    } else {
      toast("به زودی این قابلیت فعال خواهد شد");
    }
  }

  return (
    <div className="container py-3">
      <div className="shadow-profileCard flex items-center justify-between rounded-lg bg-base-100 p-3">
        <div className="flex items-center gap-3">
          <img
            className="aspect-square h-11 w-11 rounded-full"
            src={user?.avatar ?? "/app/assets/images/user/user.png"}
            alt=""
          />
          <div className="flex flex-col">
            <span className="text-sm font-bold text-base-content">
              {`${user?.first_name} ${user?.last_name}`}
            </span>
            <span className="text-xs font-medium text-base-400">
              کاربرعزیز خوش آمدید
            </span>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Link to={"/wallet"}>
            <Icon name="wallet" className="size-6 text-base-600" />
          </Link>
        </div>
      </div>

      {isLoading ? (
        <>
          <div className="mr-1 mt-6 flex items-center justify-between">
            <div className="flex items-center justify-center gap-1">
              <div className="skeleton flex h-7 w-7 items-center justify-center rounded-md" />
              <div className="skeleton h-3 w-32" />
            </div>
            <div className="btn skeleton btn-sm h-9 w-24" />
          </div>
          <div className="mt-16 flex items-center justify-center">
            <div className="shadow-profileCard skeleton relative flex h-64 w-64 items-center justify-center rounded-full p-4" />
          </div>
        </>
      ) : (
        <>
          {data?.last_test ? (
            <>
              <div className="mr-1 mt-6 flex items-center justify-between">
                <div className="flex items-center justify-center gap-1">
                  <div className="flex h-7 w-7 items-center justify-center rounded-md bg-secondary/60">
                    <Calendar className="h-6 w-6 text-base-100" />
                  </div>
                  <span className="text-sm font-bold text-base-600">
                    آخرین آنالیز :{" "}
                    <span className="text-xs font-medium text-base-400">
                      {moment(data?.last_test?.test_time)
                        .locale("fa")
                        .format("YYYY/MM/DD")}
                    </span>
                  </span>
                </div>
                <Link
                  to={`/tests/${data?.last_test?.code}`}
                  className="btn btn-secondary btn-sm text-xs"
                >
                  مشاهده گزارش
                </Link>
              </div>

              {/*  Dashboard  */}
              <div className="mt-16 flex items-center justify-center">
                <div className="shadow-profileCard relative z-10 flex h-64 w-64 items-center justify-center rounded-full bg-base-100 p-4">
                  <div
                    className="absolute inset-0 origin-center"
                    style={{
                      transform: `rotate(${
                        (data?.last_test?.metabolic_age * 266) /
                          (user?.age * 2) -
                        40
                      }deg)`,
                    }}
                  >
                    <div
                      style={{
                        left: "20px",
                      }}
                      className="absolute top-1/2 size-3 rounded-full bg-base-100 ring ring-base-100/60"
                    />
                  </div>

                  <svg
                    className="aspect-square w-full"
                    viewBox="0 0 233 233"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g clipPath="url(#clip0_4135_8)">
                      <path
                        d="M125.443 10.5439C125.793 5.79331 122.222 1.62464 117.459 1.63174C96.4329 1.66308 75.7567 7.46104 57.7162 18.4728C37.2148 30.9867 21.1462 49.6056 11.7653 71.7167C2.38438 93.8278 0.162096 118.321 5.41029 141.76C10.0285 162.385 20.2275 181.282 34.8163 196.423C38.1214 199.853 43.6003 199.524 46.7733 195.971V195.971C49.9464 192.418 49.6064 186.99 46.347 183.517C34.4223 170.808 26.0742 155.099 22.2435 137.99C17.7825 118.068 19.6714 97.2484 27.6452 78.454C35.619 59.6595 49.2773 43.8334 66.7035 33.1967C81.6677 24.0626 98.7654 19.1489 116.191 18.8922C120.954 18.8221 125.093 15.2945 125.443 10.5439V10.5439Z"
                        fill="#36C9C2"
                      />
                      <path
                        d="M188.462 195.997C191.633 199.551 197.112 199.882 200.418 196.453C208.876 187.681 215.9 177.606 221.21 166.598C227.772 152.996 231.59 138.234 232.447 123.157C233.304 108.079 231.183 92.9802 226.204 78.7223C222.175 67.1831 216.339 56.3777 208.929 46.7036C206.032 42.9219 200.551 42.6305 196.997 45.8022V45.8022C193.443 48.9739 193.168 54.4054 196.014 58.225C201.943 66.1818 206.636 75.0089 209.918 84.4087C214.15 96.5279 215.953 109.362 215.225 122.178C214.496 134.994 211.251 147.541 205.673 159.103C201.348 168.07 195.684 176.309 188.892 183.543C185.632 187.015 185.29 192.443 188.462 195.997V195.997Z"
                        fill="#FD130B"
                      />
                      <path
                        d="M209.191 47.0494C198.476 32.95 184.65 21.5156 168.79 13.6376C152.93 5.7596 135.464 1.65087 117.756 1.63169L117.737 18.8817C132.789 18.898 147.635 22.3904 161.116 29.0867C174.597 35.783 186.35 45.5022 195.457 57.4867L209.191 47.0494Z"
                        fill="#FBA403"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_4135_8">
                        <rect width="233" height="233" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>

                  <Icon
                    name="borderDashed"
                    className="absolute top-10 m-2 h-36 w-36"
                  />
                  <Icon
                    name="scales"
                    className="absolute top-16 h-20 w-20 text-secondary"
                  />
                  <span className="absolute top-32 text-center text-2xl font-normal text-base-content">
                    {data?.last_test?.metabolic_age}
                  </span>
                  <span className="absolute top-40 text-center text-xs font-normal text-base-400">
                    سال
                  </span>
                  <span className="absolute bottom-12 text-center text-xs font-normal text-base-400">
                    سن متابولیک
                  </span>
                  <span className="absolute bottom-6 text-center text-xs">
                    سن واقعی: {user?.age} سال{" "}
                  </span>
                </div>
              </div>

              <div
                onClick={handleUserFeature}
                className="mt-7 flex w-full justify-center"
              >
                <img
                  className=""
                  src="/app/assets/images/banner/bannerMealPlan.png"
                  alt=""
                />
              </div>
            </>
          ) : (
            <TestsListPlaceholder />
          )}
        </>
      )}
    </div>
  );
}
