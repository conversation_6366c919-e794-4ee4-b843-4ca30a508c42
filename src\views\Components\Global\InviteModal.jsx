import PropTypes from "prop-types";
import { toast } from "react-hot-toast";
import Button from "./Button.jsx";

export default function InviteModal({ message }) {
  return (
    <dialog id="invite_modal" className="modal px-5">
      <div className="modal-box p-0">
        <h3 className="flex items-center justify-center gap-2 bg-primary py-3 text-lg font-semibold text-primary-content">
          <span>دعوت از دوستان</span>
        </h3>
        <div className="px-4">
          <textarea
            readOnly={true}
            id="invite_message"
            className="mt-6 h-44 w-full"
            value={message}
          />
        </div>
        <div className="modal-action grid grid-cols-2 gap-3 px-5 pb-5">
          <form method="dialog">
            <Button variant="primary" className="btn-outline w-full">
              بستن
            </Button>
          </form>
          <Button
            onClick={() => {
              try {
                const message = document.getElementById("invite_message");
                message.select();
                document.execCommand("copy");
                toast.success("متن دعوتنامه در حافظه کپی شد.");
              } catch (e) {
                console.log(e);
              }
              document.getElementById("invite_modal").close();
            }}
            variant="primary"
            className="w-full"
          >
            کپی متن دعوت
          </Button>
        </div>
      </div>
    </dialog>
  );
}

InviteModal.propTypes = {
  message: PropTypes.string.isRequired,
};
