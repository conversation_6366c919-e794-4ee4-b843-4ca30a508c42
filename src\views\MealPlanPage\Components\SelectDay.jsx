import { AnimatePresence, motion } from 'motion/react';
import PropTypes from 'prop-types';

export default function SelectDay({ dates, onChange }) {
  return (
    <div
      className='flex grow snap-center flex-col items-center justify-between !gap-0 rounded-2xl bg-primary p-2 text-white'
      onClick={() => onChange(dates?.fullDate)}>
      <AnimatePresence mode='wait'>
        <motion.span
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5, ease: 'easeInOut' }}
          className='flex shrink-0 items-center justify-center border-none text-lg font-bold transition-all duration-500 ease-in-out'>
          {dates.day}
        </motion.span>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3, ease: 'easeOut' }}
          className='flex items-center opacity-100 transition-all duration-500 ease-in-out'>
          <span className='text-[10px] font-bold'>{dates.label}</span>
          <span
            className='flex items-center font-bold opacity-100 transition-all duration-500 ease-in-out before:mx-1 before:h-0.5 before:w-0.5 before:rounded-full before:bg-base-100/70'
            style={{
              fontSize: 'min(12px, 2.5vw)',
            }}>
            {dates.month}
          </span>
        </motion.div>
      </AnimatePresence>
    </div>
  );
}

SelectDay.propTypes = {
  dates: PropTypes.object.isRequired,
  onChange: PropTypes.func.isRequired,
};
