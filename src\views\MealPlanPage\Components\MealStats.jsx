import PropTypes from 'prop-types';

export default function MealStats({ item, title, color }) {
  const colors = {
    secondary: 'text-secondary bg-secondary/15',
    primary: 'text-primary bg-primary/15',
    'accent-focus': 'text-accent-focus bg-accent-focus/15',
    'accent-second': 'text-accent-second bg-accent-second/15',
  };
  return (
    <span
      className={`flex w-fit justify-center gap-0.5 rounded-full px-2 py-1 text-[10px] ${colors[color]} space-x-0.5`}>
      <span>{item?.prefix}</span>
      <span>{item?.value}</span>
      <span>{title}</span>
    </span>
  );
}

MealStats.propTypes = {
  item: PropTypes.shape({
    value: PropTypes.string,
    prefix: PropTypes.string,
  }),
  title: PropTypes.string.isRequired,
  color: PropTypes.oneOf([
    'secondary',
    'primary',
    'accent-focus',
    'accent-second',
  ]).isRequired,
};
