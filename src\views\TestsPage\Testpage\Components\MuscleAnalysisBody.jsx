import PropTypes from 'prop-types';
import DataItem from './DataItem.jsx';
import AnalysisCard from './AnalysisCard.jsx';

export default function MuscleAnalysisBody({ data, loading }) {
  if (loading) {
    return (
      <div className='m-6 flex flex-col items-center justify-center gap-4 pb-6'>
        {[...Array(5)].map((_, index) => (
          <div key={index} className='skeleton h-20 w-full'></div>
        ))}
      </div>
    );
  }

  const muscleAnalysisItems = [
    {
      title: 'وزن بدون احتساب چربی',
      name: 'ffm',
      icon: 'scales',
      unit: 'کیلوگرم',
    },
    {
      title: 'کل چربی بدن',
      name: 'fat_mass',
      icon: 'man',
      unit: 'کیلوگرم',
    },
    {
      title: 'درصد چربی بدن (PBF)',
      name: 'fat_percentage',
      icon: 'man',
      unit: 'درصد',
    },
    {
      title: 'شاخص چربی احشابی',
      name: 'visceral_fat_rating',
      icon: 'chubbyMan',
      unit: 'سطح',
    },
    {
      title: 'چربی نیم تنه بالایی',
      name: 'fat_trunk_m',
      icon: 'upperBody',
      unit: 'کیلوگرم',
    },
  ];
  return (
    <div className='m-6 flex flex-col items-center justify-center gap-4 pb-6'>
      {muscleAnalysisItems.map((item, index) => (
        <DataItem
          key={index}
          data={data}
          title={item.title}
          name={item.name}
          icon={item.icon}
          unit={item.unit}
        />
      ))}

      <div className='grid w-full grid-cols-2 gap-4'>
        <AnalysisCard
          title='چربی دست راست'
          weight={data?.test?.fat_right_arm_m}
          unit='کیلوگرم'
          percentage={data?.test?.fat_right_arm_p}
          color='secondary'
          side='left'
          type='arm'
        />

        <AnalysisCard
          title='چربی دست چپ'
          weight={data?.test?.fat_left_arm_m}
          unit='کیلوگرم'
          percentage={data?.test?.fat_left_arm_p}
          color='primary'
          side='right'
          type='arm'
        />

        <AnalysisCard
          title='چربی پا راست'
          weight={data?.test?.fat_right_leg_m}
          unit='کیلوگرم'
          percentage={data?.test?.fat_right_leg_p}
          color='primary'
          side='right'
          type='foot'
        />
        <AnalysisCard
          title='چربی پا چپ'
          weight={data?.test?.fat_left_leg_m}
          unit='کیلوگرم'
          percentage={data?.test?.fat_left_leg_p}
          color='secondary'
          side='left'
          type='foot'
        />
      </div>
    </div>
  );
}

MuscleAnalysisBody.propTypes = {
  data: PropTypes.object,
  loading: PropTypes.bool,
};
