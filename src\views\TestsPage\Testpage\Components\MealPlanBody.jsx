import PropTypes from 'prop-types';

export default function MealPlanBody({ data, loading }) {
  if (loading) {
    return (
      <div className='m-6 flex flex-col items-center justify-center gap-4 pb-6'>
        {[...Array(5)].map((_, index) => (
          <div key={index} className='skeleton h-20 w-full'></div>
        ))}
      </div>
    );
  }
  return (
    <div
      className='prose m-6 max-w-full'
      dangerouslySetInnerHTML={{ __html: data?.test?.meal_plan }}></div>
  );
}

MealPlanBody.propTypes = {
  data: PropTypes.object,
  loading: PropTypes.bool,
};
