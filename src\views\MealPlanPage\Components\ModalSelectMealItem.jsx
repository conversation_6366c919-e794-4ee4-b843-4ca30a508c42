import PropTypes from "prop-types";
import { useCallback, useEffect, useState } from "react";
import { Button, Modal } from "react-daisyui";
import { useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import api from "../../../api";
import PlanItem from "./PlanItem.jsx";

export default function ModalSelectMealItem({
  mealsData,
  toggleState,
  isOpen,
  day,
  reload,
}) {
  const [selectedMeal, setSelectedMeal] = useState(null);
  const { handleSubmit, setError } = useForm();
  const { mutate: sendMealItem, isLoading } =
    api.MealPlans.sendMealItem.useMutation();

  useEffect(() => {
    if (mealsData?.item?.food?.id) {
      const defaultSelectedMeal = mealsData.items.find(
        (meal) => meal.food.id === mealsData.item.food.id,
      );
      if (defaultSelectedMeal) {
        setSelectedMeal(defaultSelectedMeal.id);
      }
    } else {
      setSelectedMeal(null);
    }
  }, [mealsData?.item?.food?.id, mealsData?.items]);

  const submit = useCallback(
    async (data) => {
      if (!selectedMeal) {
        toast.error("لطفاً یک آیتم را انتخاب کنید.");
        return;
      }
      const res = await sendMealItem({
        ...data,
        plan_item_id: selectedMeal,
        day: day,
        meal_id: mealsData.id,
      });
      if (res.status) {
        toast.success(res.message);
        await reload();
        setSelectedMeal(null);
        toggleState(null);
      } else {
        if (typeof res.message === "string") {
          toast.error(res.message);
        } else {
          for (const item of res.message.validation) {
            setError(item.path, { message: item.msg });
          }
        }
      }
    },
    [setError, sendMealItem, selectedMeal, toggleState, mealsData, day, reload],
  );

  const handleClose = useCallback(() => {
    toggleState(null);
    if (!mealsData?.item?.id) {
      setSelectedMeal(null);
    } else {
      setSelectedMeal(mealsData.item.id);
    }
  }, [mealsData, toggleState]);

  return (
    <>
      <Modal.Legacy
        className="py-0"
        open={isOpen}
        onClickBackdrop={handleClose}
      >
        <div className="sticky top-0 z-20 flex justify-between border-b bg-base-100 py-4">
          <Modal.Header className="m-0 w-full text-lg font-bold text-base-content">
            {" "}
            لیست غذاهای مورد تایید وعده {mealsData.title}
          </Modal.Header>
          <Button onClick={handleClose} size="sm" color="ghost" shape="circle">
            ✕
          </Button>
        </div>
        <Modal.Body>
          <div className="mt-4 flex flex-col gap-2">
            {mealsData?.items?.map((meal) => (
              <PlanItem
                key={meal.id}
                meal={meal}
                mealsData={mealsData}
                setSelectedMeal={setSelectedMeal}
                selectedMeal={selectedMeal}
              />
            ))}
          </div>
        </Modal.Body>
        <Modal.Actions className="sticky bottom-0 z-10 mt-auto h-full w-full bg-base-100 pb-5 pt-2">
          <button
            onClick={handleSubmit(submit)}
            className="text-bold btn btn-primary w-full text-lg"
            disabled={isLoading}
          >
            {" "}
            ثبت
          </button>
        </Modal.Actions>
      </Modal.Legacy>
    </>
  );
}

ModalSelectMealItem.propTypes = {
  mealsData: PropTypes.shape({
    id: PropTypes.number.isRequired,
    title: PropTypes.string.isRequired,
    item: PropTypes.shape({
      id: PropTypes.string,
      food: PropTypes.shape({
        id: PropTypes.string.isRequired,
      }),
      prepare: PropTypes.shape({
        amount_of_ingredient: PropTypes.string.isRequired,
        ingredient_name: PropTypes.string.isRequired,
      }).isRequired,
    }),
    items: PropTypes.array.isRequired,
  }).isRequired,
  toggleState: PropTypes.func.isRequired,
  reload: PropTypes.func.isRequired,
  isOpen: PropTypes.bool.isRequired,
  day: PropTypes.string.isRequired,
};
