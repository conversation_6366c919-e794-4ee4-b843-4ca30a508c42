import { Reserve } from "iconsax-react";
import PropTypes from "prop-types";
import { useCallback, useRef } from "react";
import { Button, Modal } from "react-daisyui";
import ReactPlayer from "react-player";
import { useModal } from "./Modal.jsx";

export default function ModalRecipe({ item }) {
  const { isOpen, toggleState } = useModal();
  const playerRef = useRef(null);
  const stopVideo = useCallback(() => {
    if (playerRef?.current) {
      playerRef.current.getInternalPlayer().pause();
    }
  }, [playerRef]);

  const handleClose = useCallback(() => {
    toggleState();
    stopVideo();
  }, []);

  return (
    <>
      <Button
        onClick={toggleState}
        className="btn btn-outline btn-sm w-8 p-1 text-primary-focus"
      >
        <Reserve size={20} />
      </Button>
      <Modal.Legacy open={isOpen} onClickBackdrop={handleClose}>
        <div className="sticky top-0 z-10 flex w-full items-center justify-between border-b">
          <Modal.Header className="m-0 w-full text-lg font-bold text-base-content">
            طرز تهیه
          </Modal.Header>
          <Button onClick={handleClose} size="sm" color="ghost" shape="circle">
            ✕
          </Button>
        </div>
        <Modal.Body>
          {item.food.video_path && (
            <div className="mt-4 overflow-hidden rounded-lg">
              <ReactPlayer
                height="100%"
                width="100%"
                ref={playerRef}
                playing={false}
                controls={true}
                muted={false}
                style={{
                  objectFit: "cover",
                }}
                url={item.food.video_path}
              />
            </div>
          )}
          <div
            className="mt-3"
            dangerouslySetInnerHTML={{ __html: item.food.prepare }}
          />
        </Modal.Body>
        <Modal.Actions className="sticky bottom-0 mt-auto h-full w-full bg-base-100 py-2">
          <Button
            onClick={handleClose}
            className="text-bold btn btn-primary w-full text-lg"
          >
            بستن
          </Button>
        </Modal.Actions>
      </Modal.Legacy>
    </>
  );
}

ModalRecipe.propTypes = {
  item: PropTypes.shape({
    food: PropTypes.shape({
      prepare: PropTypes.string.isRequired,
      video_path: PropTypes.string.isRequired,
    }).isRequired,
  }).isRequired,
};
