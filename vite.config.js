import {defineConfig} from 'vite';
import react from '@vitejs/plugin-react';
import {VitePWA} from 'vite-plugin-pwa';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    VitePWA({
      injectRegister: 'auto',
      registerType: 'autoUpdate',
      // mode: 'development',
      base: '/app/',
      includeAssets: ['/app/assets/images/brand/logo.png'],
      manifest: {
        name: 'Top Brands',
        short_name: 'TopBrands',
        description: 'My Awesome App description',
        theme_color: '#ffffff',
        icons: [
          {
            src: '/app/assets/images/brand/logo.png',
            sizes: '192x192',
            type: 'image/png',
          },
          {
            src: '/app/assets/images/brand/logo.png',
            sizes: '512x512',
            type: 'image/png',
          },
        ],
      },
      workbox: {
        cleanupOutdatedCaches: true,
      },
      devOptions: {
        enabled: true,
        type: 'module',
        navigateFallback: 'index.html',
      },
    }),
  ],
  build: {
    // Enable cache busting with hashed filenames
    rollupOptions: {
      output: {
        entryFileNames: 'assets/[name].[hash].js',
        chunkFileNames: 'assets/[name].[hash].js',
        assetFileNames: 'assets/[name].[hash].[ext]'
      }
    }
  },
  base : "/app",
});
