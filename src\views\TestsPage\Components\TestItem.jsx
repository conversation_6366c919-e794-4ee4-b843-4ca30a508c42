import { Link } from "react-router-dom";
import Icon from "../../Components/Icon.jsx";

import { Calendar2, Location } from "iconsax-react";
import moment from "jalali-moment";
import PropTypes from "prop-types";
import indexToText from "../../../utils/indexToText.js";

export default function TestItem({ item, index }) {
  return (
    <Link
      to={`/tests/${item?.code}`}
      className="my-2.5 flex rounded-2xl bg-base-100 shadow-card"
    >
      <div className="flex items-center justify-center pr-4">
        <div className="flex h-11 w-11 items-center justify-center rounded-lg bg-primary/20">
          <Icon name={"pen"} className="h-7 w-7 text-primary-focus" />
        </div>
      </div>
      <div className="flex w-full flex-col justify-center gap-2 px-4 py-3">
        <h4 className="text-base font-bold text-base-content">
          آنالیز {indexToText(index + 1)}
        </h4>
        <div className="flex flex-col gap-2">
          <div className="flex items-center gap-1">
            <Calendar2 className="size-4 text-secondary" />
            <span className="base-400 text-sm font-medium" dir="ltr">
              {moment(item?.test_time).locale("fa").format("YYYY/MM/DD HH:mm")}
            </span>
          </div>
          <div className="flex items-center gap-1">
            <Location className="size-4 text-secondary" />
            <span className="base-400 text-sm font-medium">
              {item?.test_center?.name}
            </span>
          </div>
        </div>
      </div>
      <div className="relative w-full before:absolute before:inset-0 before:z-10 before:-mr-[1px] before:rounded-l-2xl before:rounded-r-none before:bg-test-item-gradient">
        <img
          className="z-20 aspect-[6/5] h-full w-full rounded-l-2xl rounded-r-none object-cover"
          src={item?.test_center?.image}
          alt=""
        />
        <Icon
          name={"threePoints"}
          className="absolute left-2 top-3 h-6 w-6 text-base-100"
        />
      </div>
    </Link>
  );
}

TestItem.propTypes = {
  item: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
};
