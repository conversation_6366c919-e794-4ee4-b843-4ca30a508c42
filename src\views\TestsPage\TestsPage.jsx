import { ArrowLeft } from "iconsax-react";
import { useMemo } from "react";
import { Loading } from "react-daisyui";
import { Link } from "react-router-dom";
import api from "../../api";
import Header from "../Components/Global/Header.jsx";
import TestItem from "./Components/TestItem.jsx";

export const TestsListPlaceholder = () => {
  return (
    <div className="flex h-full w-full items-center justify-center">
      <div className="mt-40 flex w-full flex-col items-center justify-center">
        <img src="/app/assets/images/tests/test.png" alt="" />
        <p className="mb-12 mt-7 text-center text-sm font-medium text-base-400">
          آنالیزی برای شما ثبت نشده است <br />
          یا داده ای هنوز دریافت نگردیده است
        </p>

        <Link
          to="/test-centers"
          className="btn btn-primary h-auto w-full text-sm"
        >
          <span>لیست مراکز آنالیز</span>
          <ArrowLeft className="size-5 text-base-100" />
        </Link>
      </div>
    </div>
  );
};

export default function TestsPage() {
  const { data: _data, isLoading } = api.Tests.list.useQuery();
  const data = useMemo(() => _data?.data, [_data]);

  return (
    <>
      <Header className="sticky top-0 z-20 flex justify-center py-5 shadow">
        <span className="font-semibold">آنالیزهای انجام شده</span>
      </Header>
      <div className="container mt-5">
        {isLoading ? (
          <>
            {[1, 2, 3, 4, 5].map((x) => (
              <div className="skeleton my-2.5 h-36" key={x} />
            ))}
          </>
        ) : (
          <>
            {!data?.length ? (
              <TestsListPlaceholder />
            ) : (
              data?.map((item, index) => (
                <TestItem key={item.id} item={item} index={index} />
              ))
            )}
          </>
        )}
      </div>
    </>
  );
}
