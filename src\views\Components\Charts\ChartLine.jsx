import {
  CategoryScale,
  Chart as ChartJS,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
  Title,
  Tooltip,
} from 'chart.js';
import PropTypes from 'prop-types';
import { Line } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
);

export default function ChartLine({ datasets = [], labels }) {
  return (
    <Line
      data={{
        labels,
        datasets: datasets.map((x) => ({
          ...x,
          borderColor: '#fff',
          pointBackgroundColor: '#FD820B',
          pointBorderColor: '#fff',
          borderWidth: 3,
          pointBorderWidth: 1,
          pointRadius: 5,
          pointHoverRadius: 5,
          tension: 0.5,
          pointHitRadius: 5,
          pointRotation: 5,
        })),
      }}
      options={{
        scales: {
          y: {
            grid: {
              display: true,
              color: 'rgba(255, 255, 255, 0.5)',
            },
            ticks: {
              font: {
                family: 'YekanBakh',
              },
              padding: 10,
              color: '#fff',
            },
          },
          x: {
            grid: {
              display: false,
              color: '#fff',
            },
            ticks: {
              font: {
                family: 'YekanBakh',
              },
              padding: 10,
              display: true,
              color: '#fff',
            },
          },
        },
        plugins: {
          legend: {
            display: false,
          },
          title: {
            display: false,
          },
        },
      }}
    />
  );
}

ChartLine.propTypes = {
  datasets: PropTypes.array.isRequired,
  labels: PropTypes.array.isRequired,
};
