import PropTypes from "prop-types";
import { useCallback, useMemo, useState } from "react";

import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import api from "../../api";
import Header from "../Components/Global/Header";
import MealPlanItem from "./Components/MealPlanItem";
import PaymentBottomSheet from "./Components/PaymentBottomSheet";

export default function PaymentPage({ prev }) {
  // Get Plans
  const { data: _data, isLoading: isPlansLoading } =
    api.Packages.list.useQuery();
  const plans = useMemo(() => _data?.data, [_data]);

  const navigate = useNavigate();

  // BottomSheet state
  const [isBottomSheetOpen, setIsBottomSheetOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);

  const { mutate: fetchPaymentOffline, isLoading: loadingBtnOffline } =
    api.Packages.paymentRequestOffline.useMutation({
      onSuccess: (response) => {
        if (response.status && response.data?.link) {
          window.location.href = response.data.link;
        } else {
          toast.error("خطا در پرداخت. لطفا دوباره تلاش کنید.");
        }
      },
      onError: (error) => {
        toast.error("خطا در پرداخت. لطفا دوباره تلاش کنید.");
      },
    });

  const { mutate: fetchPaymentOnline, isLoading: loadingBtnOnline } =
    api.Packages.paymentRequestOnline.useMutation({
      onSuccess: (response) => {
        if (response.status && response.data?.link) {
          window.location.href = response.data.link;
        } else {
          toast.error("خطا در پرداخت. لطفا دوباره تلاش کنید.");
        }
      },
      onError: (error) => {
        toast.error("خطا در پرداخت. لطفا دوباره تلاش کنید.");
      },
    });

  const handleFinalize = useCallback(
    (id) => {
      if (plans?.test?.type?.key === "PLACE") {
        fetchPaymentOffline({ package_id: id });
      } else {
        fetchPaymentOnline({ package_id: id });
      }
    },
    [plans, fetchPaymentOffline, fetchPaymentOnline],
  );

  const handlePlanSelect = useCallback((plan) => {
    setSelectedPlan(plan);
    setIsBottomSheetOpen(true);
  }, []);

  const handleBottomSheetClose = useCallback(() => {
    setIsBottomSheetOpen(false);
    setSelectedPlan(null);
  }, []);

  const handlePayment = useCallback(() => {
    if (selectedPlan) {
      handleFinalize(selectedPlan.id);
      handleBottomSheetClose();
    }
  }, [selectedPlan, handleFinalize, handleBottomSheetClose]);

  const handleBack = useCallback(() => {
    navigate(-1, { replace: true });
  }, [navigate]);

  return (
    <div className="h-dvh flex flex-col bg-base-200">
      <Header className="sticky drop-shadow-md top-0" back={handleBack}>
        <span className="font-semibold"> انتخاب پلن برنامه غذایی</span>
      </Header>
      <div className="container drop-shadow-md flex grow flex-col pt-4 ">
        <div className="card flex grow flex-col bg-base-100">
          <div className="card-body grow gap-5 px-0 pb-0 pt-4">
            <div className="flex w-full grow flex-col">
              <div className="mb-10 flex items-center px-4 pt-6 text-sm text-base-content">
                اطلاعات شما برای تهیه برنامه غذایی شخصی شما ارسال شد. لطفا با
                انتخاب پلن موردنظر خود، فرایند ثبت نام خود را تکمیل نمایید و گام
                در مسیر سلامتی بگذارید.
              </div>
              <div className="flex flex-col gap-4 px-4 pb-5">
                {isPlansLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="loading loading-spinner loading-lg text-primary" />
                  </div>
                ) : (
                  plans?.packages?.map((plan) => (
                    <MealPlanItem
                      loading={loadingBtnOffline || loadingBtnOnline}
                      key={plan.id}
                      title={`${plan.title}`}
                      price={
                        Number.parseInt(plan.discount_price) &&
                        Number.parseInt(plan.discount_price) > 0
                          ? Number.parseInt(plan.discount_price) / 10
                          : Number.parseInt(plan.main_price) / 10
                      }
                      discountPercent={
                        plan.discount_price && plan.discount_price > 0
                          ? Math.round(
                              ((Number.parseInt(plan.main_price) -
                                Number.parseInt(plan.discount_price)) /
                                Number.parseInt(plan.main_price)) *
                                100,
                            )
                          : undefined
                      }
                      discountPrice={
                        Number.parseInt(plan.discount_price) &&
                        Number.parseInt(plan.discount_price) > 0
                          ? Number.parseInt(plan.main_price) / 10
                          : undefined
                      }
                      color={plan.id % 2 ? "secondary" : "primary"}
                      onClick={() => handlePlanSelect(plan)}
                    />
                  ))
                )}
              </div>

              <div className="sticky bottom-0 mt-auto w-full bg-base-100 px-4 pb-5 pt-5 text-center">
                <button
                  onClick={prev ? prev : handleBack}
                  className="btn btn-primary w-full text-base font-semibold"
                >
                  بازگشت
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Bottom Sheet */}
      {selectedPlan && (
        <PaymentBottomSheet
          planTitle={selectedPlan.title}
          planPrice={
            Number.parseInt(selectedPlan.discount_price) &&
            Number.parseInt(selectedPlan.discount_price) > 0
              ? Number.parseInt(selectedPlan.discount_price) / 10
              : Number.parseInt(selectedPlan.main_price) / 10
          }
          onPayment={handlePayment}
          loading={loadingBtnOffline || loadingBtnOnline}
          isOpen={isBottomSheetOpen}
          onClose={handleBottomSheetClose}
        />
      )}
    </div>
  );
}

PaymentPage.propTypes = {
  prev: PropTypes.func,
};
