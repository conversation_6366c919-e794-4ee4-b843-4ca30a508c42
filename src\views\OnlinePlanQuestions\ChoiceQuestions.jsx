import PropTypes from "prop-types";
import { use<PERSON><PERSON>roller } from "react-hook-form";
import { cn } from "../../utils/utils";

const ChoiceQuestions = ({
  items,
  control,
  rules,
  name,
  defaultValue,
  orientation = "vertical",
  className,
  type = "radio",
  required,
  onChange,
}) => {
  const { field } = useController({
    control,
    name,
    rules: {
      required: required ? "این سوال اجباری است" : false,
      ...rules,
    },
    defaultValue: defaultValue ?? (type === "checkbox" ? [] : ""),
  });

  return (
    <ul
      className={cn(
        "w-full gap-2 px-4 pt-4",
        {
          "flex flex-col": orientation === "vertical",
          "flex flex-row gap-4 px-6 ": orientation === "horizontal",
        },
        className,
      )}
    >
      {items.map((item, index) => {
        const id = `${item.inputName}_${index}`;

        const isChecked =
          type === "checkbox"
            ? Array.isArray(field.value) && field.value.includes(item.value)
            : field.value === item.value;

        return (
          <li key={id} className="w-full">
            <input
              {...field}
              type={type}
              value={item.value}
              id={id}
              className="peer hidden"
              checked={isChecked}
              onChange={() => {
                if (type === "checkbox") {
                  if (isChecked) {
                    field.onChange(
                      field.value.filter((value) => value !== item.value),
                    );
                  } else {
                    field.onChange([...field.value, item.value]);
                  }
                } else {
                  field.onChange(item.value);
                }
                // فراخوانی onChange prop در صورت وجود
                if (onChange) {
                  onChange();
                }
              }}
            />
            <label
              htmlFor={id}
              className="flex w-full items-center gap-2 rounded-md border-2 border-base-300 px-2 py-2 transition-colors peer-checked:border-secondary peer-checked:*:border-base-100 peer-checked:*:bg-secondary peer-checked:*:ring-secondary"
            >
              <span className="size-4 shrink-0 rounded-full border-2 border-base-300 ring-2 ring-transparent ring-offset-0" />
              {item.label}
            </label>
          </li>
        );
      })}
    </ul>
  );
};

ChoiceQuestions.propTypes = {
  items: PropTypes.arrayOf(
    PropTypes.shape({
      inputName: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
      defaultChecked: PropTypes.bool,
    }),
  ).isRequired,
  control: PropTypes.object.isRequired,
  name: PropTypes.string.isRequired,
  rules: PropTypes.object,
  defaultValue: PropTypes.string,
  orientation: PropTypes.oneOf(["vertical", "horizontal"]),
  className: PropTypes.string,
  type: PropTypes.oneOf(["radio", "checkbox"]),
  required: PropTypes.bool,
  onChange: PropTypes.func,
};

export default ChoiceQuestions;
