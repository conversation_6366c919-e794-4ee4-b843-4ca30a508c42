import { Chart2 } from "iconsax-react";
import PropTypes from "prop-types";
import { useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { twMerge } from "tailwind-merge";

const DataItemStatistic = ({ data, name, unit, title, variant }) => {
  const template = useMemo(() => {
    return data?.templates?.find((item) => item.name === name);
  }, [data, name]);

  const navigate = useNavigate();

  return (
    <div
      onClick={() => {
        template && navigate(name);
      }}
      className="flex grow items-center justify-between gap-3 px-3"
    >
      <div className="flex flex-col">
        <span className="text-sm font-bold text-base-400">{title}</span>
        <span className="text-sm font-normal text-base-400">
          <span className="font-bold text-base-content">
            {data?.test?.[name]}
          </span>
          &nbsp;&nbsp;{unit}
        </span>
      </div>

      {template && (
        <div className="shrink-0">
          <Chart2
            className={twMerge(
              "size-6",
              {
                primary: "text-primary",
                secondary: "text-secondary",
              }[variant],
            )}
            variant="Bulk"
          />
        </div>
      )}
    </div>
  );
};

DataItemStatistic.propTypes = {
  data: PropTypes.object.isRequired,
  name: PropTypes.string.isRequired,
  unit: PropTypes.string.isRequired,
  title: PropTypes.string.isRequired,
  variant: PropTypes.string.isRequired,
};

export default DataItemStatistic;
