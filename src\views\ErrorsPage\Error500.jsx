import PropTypes from "prop-types";
import { useNavigate } from "react-router-dom";

export default function Error500({ reset }) {
  const navigate = useNavigate();
  return (
    <section className="relative z-10 flex h-dvh flex-col items-center justify-center overflow-hidden">
      <div
        className="absolute inset-0 -z-10 bg-contain bg-left bg-no-repeat opacity-20"
        style={{
          backgroundImage: "url('/assets/images/brand/logo.svg')",
        }}
      />
      <h1 className="flex items-center text-[8rem] md:text-[13rem] font-bold leading-[8rem] md:leading-[13rem]">
        500
      </h1>

      <h2 className="text-xl md:text-3xl font-bold">خطای سیستمی</h2>
      <h2 className="mt-4  md:text-2xl font-medium text-center px-4">
        مشکلی رخ داده است . همکاران ما درحال برسی این مشکل هستند. از صبر و
        شکیبایی شما سپاس گزاریم.
      </h2>
      <button
        onClick={() => {
          reset();
          navigate("/");
        }}
        className="btn btn-secondary mt-4  md:mt-10 rounded-full px-6 md:px-10 text-sm md:text-base"
      >
        بازگشت به صفحه اصلی
      </button>
    </section>
  );
}

Error500.propTypes = {
  reset: PropTypes.func.isRequired,
};
