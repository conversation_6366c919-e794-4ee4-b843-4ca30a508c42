import { router } from "react-query-kit";
import createFetcher from "../utils/createFetcher";

const Profile = router("profile", {
  detail: router.query({
    fetcher: createFetcher("/v1/front/user/profile"),
  }),
  update: router.mutation({
    mutationFn: createFetcher("/v1/front/user/profile", "POST"),
  }),
  updateGroups: router.mutation({
    mutationFn: createFetcher("/v1/front/user/profile/groups", "POST"),
  }),
});

export default Profile;
