import PropTypes from 'prop-types';
import Icon from '../../../Components/Icon';
import { twMerge } from 'tailwind-merge';

const MuscleAnalysisCard = ({ title, weight, unit, color, side, type }) => {
  const bgColor =
    color === 'primary'
      ? 'from-primary to-primary-focus'
      : 'from-secondary-focus to-secondary';

  return (
    <div
      className={`relative aspect-square rounded-3xl bg-gradient-to-bl ${bgColor} text-base-100`}>
      <div className='relative flex flex-col pt-3 font-extrabold'>
        <span className='pr-3 text-base'>{title}</span>
        <div className='absolute right-4 top-11 flex flex-col'>
          <span className='text-3xl font-normal'>{weight}</span>
          <span className='text-xs font-normal'>{unit}</span>
        </div>
      </div>
      <Icon
        className={twMerge(
          'absolute bottom-0 size-9 text-base-100',
          side === 'left' && 'right-0',
          side === 'right' && 'left-0 -scale-x-100',
        )}
        name='quarterCircle'
      />
      <Icon
        className={twMerge(
          'absolute',
          side === 'left' && 'left-0 -scale-x-100',
          side === 'right' && 'right-0',
          type === 'arm' && '-bottom-1.5 h-28 w-16',
          type === 'foot' && 'bottom-0 h-32 w-14',
        )}
        name={type === 'arm' ? 'pattern6' : 'pattern7'}
      />
    </div>
  );
};

MuscleAnalysisCard.propTypes = {
  title: PropTypes.string.isRequired,
  weight: PropTypes.number.isRequired,
  unit: PropTypes.string.isRequired,
  type: PropTypes.oneOf(['arm', 'foot']).isRequired,
  color: PropTypes.oneOf(['primary', 'secondary']).isRequired,
  side: PropTypes.oneOf(['left', 'right']).isRequired,
};

export default MuscleAnalysisCard;
