import { User } from "iconsax-react";
import toast from "react-hot-toast";
import { Link, Outlet, useLocation } from "react-router-dom";
import useUserStore from "../store/userStore"; // Import Zustand user store
import Icon from "./Components/Icon";

export default function HomeLayout() {
  const user = useUserStore((state) => state.user);
  const links = [
    {
      id: 1,
      IconName: "dietPlan",
      title: "برنامه غذایی",
      hasAccess: true,
      link: "/",
    },
    {
      id: 2,
      IconName: "force",
      title: "آنالیز بدن",
      hasAccess: true,
      link: "/tests",
    },
    {
      id: 3,
      IconName: "chart",
      title: "سوابق آنالیز",
      link: "/test-records",
      hasAccess: user?.is_feature ?? false,
    },
    {
      id: 4,
      IconName: User,
      title: "پروفایل",
      link: "/profile",
      hasAccess: true,
    },
  ];

  const location = useLocation();

  return (
    <section className="flex h-full relative  min-h-dvh flex-col bg-base-200">
      <div className="flex pb-20 grow flex-col">
        <Outlet />
      </div>
      {/* Bottom nav */}
      <div className="fixed bottom-0  inset-x-0 sm:inset-x-auto  mx-auto sm:w-96 z-20 grid grid-cols-4 bg-base-100 px-5 py-3 text-sm text-base-500 shadow-card">
        {links?.map(({ IconName, ...item }) => {
          const Component = item.hasAccess ? Link : "div";
          return (
            <Component
              key={item.id}
              to={item.link}
              onClick={() => {
                if (!item.hasAccess) {
                  toast("این قابلیت به زودی فعال خواهد شد");
                }
              }}
              open={location.pathname === item.link}
              className="relative flex flex-col items-center gap-1 text-xs font-semibold transition-all duration-300 before:absolute before:bottom-0 before:w-0 before:rounded-t-lg before:transition-all before:duration-300 open:text-base-100"
            >
              <img
                open={location.pathname === item.link}
                className="absolute z-20 h-10 max-w-[unset] opacity-0 open:-top-3 open:opacity-100 open:transition-all open:duration-300"
                src="/app/assets/images/background/Union.png"
                alt=""
              />
              <div
                open={location.pathname === item.link}
                className="z-30 flex items-center justify-center rounded-full open:absolute open:-top-9 open:h-14 open:w-14 open:bg-gradient-to-b open:from-primary-focus open:to-primary open:transition-all open:duration-300"
              >
                {typeof IconName === "string" ? (
                  <Icon name={IconName} className="size-7" />
                ) : (
                  <IconName className="size-7" />
                )}
              </div>
              <span>{item.title}</span>
            </Component>
          );
        })}
      </div>
    </section>
  );
}
