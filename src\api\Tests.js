import { router } from "react-query-kit";
import createFetcher from "../utils/createFetcher";

const Tests = router("tests", {
  list: router.query({
    fetcher: createFetcher("/v1/front/user/tests"),
  }),
  detail: router.query({
    fetcher: createFetcher("/v1/front/user/tests/:id"),
  }),
  history: router.query({
    fetcher: createFetcher("/v1/front/user/tests/:id/details"),
  }),
  target: router.query({
    fetcher: createFetcher("/v1/front/tests/target"),
  }),
});

export default Tests;
