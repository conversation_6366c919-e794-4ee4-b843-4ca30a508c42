import { useMemo } from "react";
import api from "../../api/index.js";
import LoadingPage from "../ErrorsPage/LoadingPage.jsx";
import ResultStep from "./../RequestMealPlanPage/Components/ResultStep";
import HasPlanWrapper from "./HasPlanWrapper.jsx";
import SelectAnalysisType from "./SelectAnalysisType.jsx";

export default function MealPlanPage() {
  // Get Plans Last
  const { data: _data, isLoading } = api.MealPlans.last.detail.useQuery();
  const data = useMemo(() => _data?.data, [_data]);

  if (isLoading) {
    return <LoadingPage />;
  }

  if (data?.plan) {
    if (data?.plan?.status?.key === "PENDING") {
      return <ResultStep />;
    }
    return <HasPlanWrapper data={data} />;
  }
  return <SelectAnalysisType data={data} />;
}
