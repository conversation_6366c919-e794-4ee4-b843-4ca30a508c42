import { twMerge } from 'tailwind-merge';
import clsx from 'clsx';
import Icon from '../../Components/Icon.jsx';
import MealStats from './MealStats.jsx';
import PropTypes from 'prop-types';
import { useMemo } from 'react';

const defaultImage = {
  breakfast: '/app/assets/images/icons/breakfast.png',
  lunch: '/app/assets/images/icons/lunch.png',
  dinner: '/app/assets/images/icons/dinner.png',
  morning_snack: '/app/assets/images/icons/snack.png',
  afternoon_snack: '/app/assets/images/icons/snack.png',
};

export default function PlanItem({
  meal,
  selectedMeal,
  setSelectedMeal,
  mealsData,
}) {
  const remainingCapacity = useMemo(() => {
    return Number(meal.capacity) - Number(meal.used_count);
  }, [meal]);

  const isDisabled = useMemo(() => {
    return !remainingCapacity && mealsData?.item?.id !== meal.id;
  }, [remainingCapacity, mealsData, meal]);

  return (
    <div
      className={twMerge(
        'shadow-profileCard flex cursor-pointer flex-col items-center rounded-lg border border-base-300 p-3',
        clsx({
          'border-primary': selectedMeal === meal.id,
          'border-base-200 bg-base-200': isDisabled,
        }),
      )}
      onClick={() => !isDisabled && setSelectedMeal(meal.id)}>
      <div className='flex w-full items-center justify-between gap-5'>
        <div className='relative flex w-full items-center gap-5'>
          {selectedMeal === meal.id ? (
            <Icon
              name='tick'
              className='absolute top-0 z-10 h-5 w-5 rounded-md bg-primary text-base-100'
            />
          ) : (
            !isDisabled && (
              <span className='absolute top-0 z-10 h-5 w-5 rounded-md border-2 border-base-300 bg-base-100' />
            )
          )}
          <img
            className={twMerge(
              'aspect-square h-20 w-20 rounded-lg',
              clsx({
                'bg-base-200 p-5 grayscale': !meal.food.image,
              }),
            )}
            src={
              meal.food.image
                ? meal.food.image
                : (defaultImage[mealsData?.name] ??
                  '/app/assets/images/icons/breakfast.png')
            }
            alt={meal.food.title}
          />
          <div className='flex w-full flex-col'>
            <div className='flex w-full flex-col'>
              <span className='flex items-center text-sm font-bold text-base-content'>
                {meal.food.title}
              </span>
              <div className='line-clamp-1 items-center gap-1'>
                {meal?.prepare?.map((p, index) => (
                  <div key={index}>
                    <span>
                      <span className='my-2 text-xs text-base-content/80'>
                        {p.amount}
                      </span>
                      &nbsp;
                      <span className='my-2 text-xs text-base-content/80'>
                        {p.name}
                      </span>
                      &nbsp;
                      {index !== meal.prepare.length - 1 && (
                        <span className='text-secondary'>+</span>
                      )}
                      &nbsp;
                    </span>
                  </div>
                ))}
              </div>
            </div>
            <MealStats
              item={meal.calories}
              title={'کالری'}
              color={'accent-second'}
            />
          </div>
        </div>
        <div className='flex shrink-0 flex-col items-center gap-2'>
          <span className='flex h-10 w-10 items-center justify-center rounded-full border font-bold text-base-content'>
            {remainingCapacity}
          </span>
          <span className='text-sm text-base-400'>
            از <span className='text-secondary'>{meal.capacity}</span> وعده
          </span>
        </div>
      </div>
    </div>
  );
}

PlanItem.propTypes = {
  mealsData: PropTypes.shape({
    name: PropTypes.string.isRequired,
    item: PropTypes.shape({
      id: PropTypes.string.isRequired,
    }),
  }).isRequired,
  meal: PropTypes.shape({
    id: PropTypes.string.isRequired,
    used_count: PropTypes.string.isRequired,
    capacity: PropTypes.string.isRequired,
    protein: PropTypes.string.isRequired,
    lipid: PropTypes.string.isRequired,
    carbohydrate: PropTypes.string.isRequired,
    calories: PropTypes.string.isRequired,
    prepare: PropTypes.arrayOf(
      PropTypes.shape({
        amount_of_ingredient: PropTypes.string,
        ingredient_name: PropTypes.string,
      }).isRequired,
    ),
    food: PropTypes.shape({
      title: PropTypes.string.isRequired,
      image: PropTypes.string,
    }),
  }).isRequired,
  setSelectedMeal: PropTypes.func.isRequired,
  selectedMeal: PropTypes.string.isRequired,
};
