import { twMerge } from 'tailwind-merge';
import clsx from 'clsx';
import Icon from '../../Components/Icon.jsx';
import PropTypes from 'prop-types';

Stepper.propTypes = {
  steps: PropTypes.arrayOf(PropTypes.object),
  currentStep: PropTypes.number.isRequired,
};

export default function Stepper({ steps = [], currentStep }) {
  return (
    <ul className='steps flex items-center justify-center gap-2'>
      {steps?.map((step, i) => (
        <li key={i} className='group flex items-center justify-center gap-2'>
          <div
            className={twMerge(
              'flex h-8 w-8 items-center justify-center rounded-full bg-base-400/20',
              clsx({
                'bg-primary bg-primary/20': currentStep >= i,
              }),
            )}>
            <span
              className={twMerge(
                'flex h-3.5 w-3.5 items-center justify-center rounded-full border-2 border-base-400 bg-base-100',
                clsx({
                  'border-primary': currentStep >= i,
                }),
              )}>
              {currentStep > i && (
                <Icon
                  name='tick'
                  className='h-3.5 w-3.5 rounded-full bg-primary text-base-100'
                />
              )}
            </span>
          </div>

          {i < steps.length - 1 && (
            <span className='font-bold text-base-300'>.....</span>
          )}
        </li>
      ))}
    </ul>
  );
}
