import moment from "jalali-moment";
import PropTypes from "prop-types";
import { useMemo } from "react";
import { useForm, useWatch } from "react-hook-form";
import Button from "../Global/Button";
import <PERSON>Field from "./SelectField";

export default function DateSelectDialog({ id, onChange, label, value }) {
  const currentValue = useMemo(() => {
    return value ? moment(value).format("jYYYY/jMM/jDD") : undefined;
  }, [value]);

  const form = useForm({
    mode: "onChange",
    values: currentValue
      ? {
          year: +currentValue.split("/")[0],
          month: +currentValue.split("/")[1],
          day: +currentValue.split("/")[2],
        }
      : {
          year: undefined,
          month: undefined,
          day: undefined,
        },
  });

  const year = useWatch({
    control: form.control,
    name: "year",
  });

  const month = useWatch({
    control: form.control,
    name: "month",
  });

  const years = useMemo(() => {
    const currentYear = moment().locale("fa").year();
    return Array.from(Array(101).keys())
      .map((x) => ({
        value: currentYear - (100 - x),
        label: currentYear - (100 - x),
      }))
      .reverse();
  }, []);

  const months = useMemo(() => {
    return Array.from(Array(12).keys()).map((x) => ({
      value: x + 1,
      label: moment(`${(x + 1).toString().padStart(2, "0")}`, "jMM")
        .locale("fa")
        .format("MMM"),
    }));
  }, []);
  const days = useMemo(() => {
    const daysCount = moment(
      `${year}/${(month ?? 0).toString().padStart(2, "0")}`,
      "jYYYY/jMM",
    )
      .locale("fa")
      .daysInMonth();
    return Array.from(Array(daysCount).keys()).map((x) => ({
      value: x + 1,
      label: x + 1,
    }));
  }, [month, year]);

  return (
    <dialog id={`${id}dialog`} className="modal px-5">
      <div className="modal-box p-0">
        <h3 className="flex items-center justify-center gap-2 py-3 text-lg font-semibold">
          <span>{label}</span>
        </h3>

        <div className="mt-4 grid grid-cols-3 gap-3 px-5">
          <SelectField
            floatingLabel
            form={form}
            name={"day"}
            label="روز"
            options={days}
            rules={{
              required: {
                value: true,
              },
            }}
          />
          <SelectField
            floatingLabel
            form={form}
            name={"month"}
            label="ماه"
            options={months}
            rules={{
              required: {
                value: true,
              },
            }}
          />

          <SelectField
            floatingLabel
            form={form}
            name={"year"}
            label="سال"
            options={years}
            rules={{
              required: {
                value: true,
              },
            }}
          />
        </div>

        <div className="modal-action grid grid-cols-2 gap-3 px-5 pb-5">
          <form method="dialog">
            <Button variant="secondary" className="btn-outline w-full">
              بستن
            </Button>
          </form>
          <Button
            onClick={form.handleSubmit((data) => {
              const date = moment(
                `${data.year}/${data.month?.toString().padStart(2, "0")}/${data.day?.toString()?.padStart(2, "0")}`,
                "jYYYY/jMM/jDD",
              ).format("YYYY/MM/DD");
              onChange(date);
              document.getElementById(`${id}dialog`).close();
            })}
            variant="secondary"
            disable={!form.formState.isValid}
            className="w-full"
          >
            تایید
          </Button>
        </div>
      </div>
    </dialog>
  );
}

DateSelectDialog.propTypes = {
  id: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  label: PropTypes.string.isRequired,
  value: PropTypes.string,
};
