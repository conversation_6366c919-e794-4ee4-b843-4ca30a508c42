import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import api from "../api/index.js";
import useThemeStore from "../store/themeStore.js"; // Import Zustand theme store
import useUserStore from "../store/userStore.js"; // Import Zustand user store
import { getAuthToken, getStorage } from "../utils/utils.js"; // Import getAuthToken

/**
 * Custom hook for initializing the application
 * Handles authentication, user data, and theme settings
 */
export function useAppInitialization() {
  const logout = useUserStore((state) => state.logout);
  const setUser = useUserStore((state) => state.setUser);
  const token = getAuthToken(); // Get token directly using getAuthToken
  const setSettings = useThemeStore((state) => state.setSettings);

  // Query user data only when authentication token exists
  const { data: meData, isLoading: isMeLoading } = api.Auth.me.useQuery({
    enabled: !!token,
  });

  // Query application settings regardless of authentication status
  const { data: settingsData, isLoading: isSettingsLoading } =
    api.Settings.detail.useQuery();

  const navigate = useNavigate();
  const location = useLocation();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  // Track loading state for both authentication and settings initialization
  const [isLoading, setIsLoading] = useState(true);

  // Handle initial authentication check and invite code processing
  useEffect(() => {
    async function checkAuth() {
      // Process invite code if present in URL
      if (new URLSearchParams(location.search).get("invite")) {
        await getStorage().setItem(
          "INVITE_CODE",
          new URLSearchParams(location.search).get("invite"),
        );
      }

      // Redirect to login if no authentication token exists
      if (!token) {
        navigate("/login");
        setIsAuthenticated(false);
        return;
      }
    }

    checkAuth();
  }, [token, location.search]);

  // Process user data and manage authentication state
  useEffect(() => {
    if (!isMeLoading) {
      if (meData?.status) {
        setUser(meData.data);
        setIsAuthenticated(true);
      } else {
        // Clear session and redirect on authentication failure
        logout(navigate); // Pass navigate to logout action
        setIsAuthenticated(false);
      }
    }
  }, [isMeLoading, meData, logout, navigate, setUser]); // Update dependencies

  // Initialize application settings when data is available
  useEffect(() => {
    if (!isSettingsLoading && settingsData?.status) {
      setSettings(settingsData.data);
    } else if (!isSettingsLoading && !settingsData?.status) {
      console.error("Failed to fetch settings:", settingsData?.message);
    }
  }, [isSettingsLoading, settingsData]);

  // Manage global loading state based on data fetching progress
  useEffect(() => {
    setIsLoading(isMeLoading || isSettingsLoading);
  }, [isMeLoading, isSettingsLoading]);

  return { isAuthenticated, isLoading };
}
