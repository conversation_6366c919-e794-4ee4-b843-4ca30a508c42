@font-face {
  font-family: YekanBakh;
  font-style: normal;
  font-weight: 100;
  src: url("/assets/fonts/yekanBakh/Yekan-Bakh-FaNum-01-Hairline.woff")
    format("woff"),
    url("/assets/fonts/yekanBakh/Yekan-Bakh-FaNum-01-Hairline.eot")
    format("eot"),
    url("/assets/fonts/yekanBakh/Yekan-Bakh-FaNum-01-Hairline.ttf")
    format("ttf");
}

@font-face {
  font-family: YekanBakh;
  font-style: normal;
  font-weight: 200;
  src: url("/assets/fonts/yekanBakh/Yekan-Bakh-FaNum-02-Thin.woff")
    format("woff"), url("/assets/fonts/yekanBakh/Yekan-Bakh-FaNum-02-Thin.eot")
    format("eot"), url("/assets/fonts/yekanBakh/Yekan-Bakh-FaNum-02-Thin.ttf")
    format("ttf");
}

@font-face {
  font-family: YekanBakh;
  font-style: normal;
  font-weight: 300;
  src: url("/assets/fonts/yekanBakh/Yekan-Bakh-FaNum-03-Light.woff")
    format("woff"), url("/assets/fonts/yekanBakh/Yekan-Bakh-FaNum-03-Light.eot")
    format("eot"), url("/assets/fonts/yekanBakh/Yekan-Bakh-FaNum-03-Light.ttf")
    format("ttf");
}

@font-face {
  font-family: YekanBakh;
  font-style: normal;
  font-weight: 400;
  src: url("/assets/fonts/yekanBakh/Yekan-Bakh-FaNum-04-Regular.eot")
    format("woff"),
    url("/assets/fonts/yekanBakh/Yekan-Bakh-FaNum-04-Regular.eot") format("eot"),
    url("/assets/fonts/yekanBakh/Yekan-Bakh-FaNum-04-Regular.ttf") format("ttf");
}

@font-face {
  font-family: YekanBakh;
  font-style: normal;
  font-weight: 500;
  src: url("/assets/fonts/yekanBakh/Yekan-Bakh-FaNum-05-Medium.woff")
    format("woff"),
    url("/assets/fonts/yekanBakh/Yekan-Bakh-FaNum-05-Medium.eot") format("eot"),
    url("/assets/fonts/yekanBakh/Yekan-Bakh-FaNum-05-Medium.ttf") format("ttf");
}

@font-face {
  font-family: YekanBakh;
  font-style: normal;
  font-weight: 600;
  src: url("/assets/fonts/yekanBakh/Yekan-Bakh-FaNum-06-Bold.woff")
    format("woff"), url("/assets/fonts/yekanBakh/Yekan-Bakh-FaNum-06-Bold.eot")
    format("eot"), url("/assets/fonts/yekanBakh/Yekan-Bakh-FaNum-06-Bold.ttf")
    format("ttf");
}

@font-face {
  font-family: YekanBakh;
  font-style: normal;
  font-weight: 700;
  src: url("/assets/fonts/yekanBakh/Yekan-Bakh-FaNum-07-Heavy.woff")
    format("woff"), url("/assets/fonts/yekanBakh/Yekan-Bakh-FaNum-07-Heavy.eot")
    format("eot"), url("/assets/fonts/yekanBakh/Yekan-Bakh-FaNum-07-Heavy.ttf")
    format("ttf");
}

@font-face {
  font-family: YekanBakh;
  font-style: normal;
  font-weight: 800;
  src: url("/assets/fonts/yekanBakh/Yekan-Bakh-FaNum-08-Fat.woff")
    format("woff"), url("/assets/fonts/yekanBakh/Yekan-Bakh-FaNum-08-Fat.eot")
    format("eot"), url("/assets/fonts/yekanBakh/Yekan-Bakh-FaNum-08-Fat.ttf")
    format("ttf");
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply font-primary;
  }

  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
}

@layer components {
  .form-control label {
    transition: all 0.2s ease-in-out;
    pointer-events: none;
  }

  .form-control select:focus ~ label,
  .form-control select:not([value=""]) ~ label,
  .form-control input:focus ~ label,
  .form-control input:not(:placeholder-shown) ~ label {
    top: -0.25rem;
    height: 25%;
    font-size: 0.75rem;
    --tw-translate-y: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
      rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
      scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .clip-path {
    clip-path: polygon(72% 0%, 100% 100%, 39% 100%, 0% 100%, 0% 54%, 0% 0%);
  }
}

.swiper-button-prev,
.swiper-button-next {
  @apply bg-white/50 !text-white !w-8 !h-8 flex items-center justify-center rounded-full aspect-square after:aspect-square after:p-1 after:text-center after:justify-center after:shrink-0;

  &:after {
    font-size: 16px !important;
  }
}
