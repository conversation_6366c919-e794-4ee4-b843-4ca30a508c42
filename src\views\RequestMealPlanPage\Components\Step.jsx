import PropTypes from "prop-types";
import QuestionMultipleChoice from "./QuestionMultipleChoice.jsx";
import QuestionOneChoice from "./QuestionOneChoice.jsx";

export default function Step({ item = {}, value = [], onChange }) {
  return (
    <div className="mt-8 flex flex-col">
      <p className="mb-4 text-center text-sm font-normal text-base-400">
        {item?.description}
      </p>
      {item?.questions?.map(
        (question) =>
          ({
            ONE_CHOICE: (
              <QuestionOneChoice
                item={question}
                value={value}
                onChange={onChange}
                key={question.id}
              />
            ),
            MULTIPLE_CHOICE_DESCRIPTIVE: (
              <QuestionMultipleChoice
                item={question}
                value={value}
                onChange={onChange}
                key={question.id}
                haveDescription={true}
              />
            ),
            ONE_CHOICE_DESCRIPTIVE: (
              <QuestionOneChoice
                item={question}
                value={value}
                onChange={onChange}
                key={question.id}
              />
            ),
            MULTIPLE_CHOICE: (
              <QuestionMultipleChoice
                item={question}
                value={value}
                onChange={onChange}
                key={question.id}
              />
            ),
          })[question.type.key],
      )}
    </div>
  );
}

Step.propTypes = {
  item: PropTypes.object.isRequired,
  value: PropTypes.array.isRequired,
  onChange: PropTypes.func.isRequired,
};
