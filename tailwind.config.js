/** @type {import('tailwindcss').Config} */
import daisyui from 'daisyui';
import themes from 'daisyui/src/theming/themes';
import typography from '@tailwindcss/typography';

export default {
  content: [
    './index.html',
    './src/**/*.{js,ts,jsx,tsx}',
    'node_modules/daisyui/dist/**/*.js',
    'node_modules/react-daisyui/dist/**/*.js',
  ],
  theme: {
    extend: {
      container: {
        center: true,
        padding: '1rem',
      },
      colors: {
        'primary-focus': '#21A8B4',
        'secondary-focus': '#FFB56D',
        'accent-focus': '#2153B4',
        'accent-second': '#9133DB',
        'accent-second-focus': '#5921B4',
        'base-400': '#AAACB8',
        'base-500': '#707070',
        'base-600': '#000000',
      },
      fontFamily: {
        primary: 'YekanBakh',
      },
      boxShadow: {
        'card': '2px 2px 10px rgba(0, 0, 0, 0.05)',
        'profileCard': ' box-shadow: 2px 0px 10px rgba(0, 0, 0, 0.1)',
      },
      backgroundImage: {
        'test-item-gradient': 'linear-gradient(75deg, rgba(54, 201, 194, 0.2) 2.59%, #FFFFFF 84.87% )',
        'bg-login': 'url(\'/app/assets/images/background/bg-login.png\')',
        'bg-white': 'url(\'/app/assets/images/background/bg-white.png\')',
        'pattern': 'url(\'/app/assets/images/otp/pattern.png\')',
      },
    },
  },
  daisyui: {
    rtl: true,
    themes: [
      {
        light: {
          ...themes.light,
          primary: '#36C9C2',
          'primary-focus': '#21A8B4',
          'primary-content': themes.light['base-100'],
          '--rounded-btn': '50px',
          'secondary': '#FD820B',
          'secondary-focus': '#FFB56D',
          'secondary-content': themes.light['base-100'],
          'accent': '#33B3DB',
          'accent-focus': '#2153B4',
          'accent-content': themes.light['base-100'],
          'base-100': '#FFFFFF',
          'base-200': '#F6F6F6',
          'base-300': '#DDDDDD',
          'base-content': '#161A23',
        },
      },
    ],
  },
  plugins: [
    daisyui,
    typography,
  ],
};

