name: 🚀 Deploy website on push
on:
  push:
    branches: [ main ]
jobs:
  web-deploy:
    name: 🎉 Deploy
    runs-on: ubuntu-latest
    timeout-minutes: 15
    steps:
      - name: 🚚 Get latest code
        uses: actions/checkout@v4

      - name: Use Node.js 18
        uses: actions/setup-node@v2
        with:
          node-version: '18.x'

      - name: 🔨 Build Project
        run: |
          npm install
          npm run build

      - name: 📂 Sync files
        uses: SamKirkland/FTP-Deploy-Action@v4.3.5
        with:
          server: app.topbrands.ir
          username: apptopbrands
          password: ${{ secrets.password }}
          server-dir: ./public_html/
          local-dir: ./dist/
          timeout: 300000