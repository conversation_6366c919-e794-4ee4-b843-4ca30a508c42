import { AgCharts } from "ag-charts-react";
import moment from "jalali-moment";
import { useMemo } from "react";
import { useNavigate, useParams } from "react-router-dom";
import api from "../../../api";
import Header from "../../Components/Global/Header.jsx";

export default function HistoryPage() {
  const params = useParams();
  const { data: _data, isLoading } = api.Tests.history.useQuery({
    variables: { id: params.id, template: params.template },
  });
  const data = useMemo(() => _data?.data, [_data]);
  const navigate = useNavigate();
  return (
    <div>
      <div className="rounded-b-3xl bg-gradient-to-tl from-primary to-primary-focus shadow-card">
        <Header
          backClassName="text-base-100 bg-base-100/15 rounded-lg p-0"
          className="bg-transparent"
          back={() => {
            navigate(-1);
          }}
        >
          <span className="font-semibold text-base-100">
            {data?.template?.translate}
          </span>
        </Header>
        <div className="relative px-5 pb-20">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <span className="loading loading-spinner" />
            </div>
          ) : (
            <AgCharts
              options={{
                data: data?.tests?.map((item) => ({
                  value: item?.[params.template],
                  label: moment(item.test_time)
                    .locale("fa")
                    .format("YYYY/MM/DD"),
                })),
                series: [
                  {
                    type: "line",
                    xKey: "label",
                    yKey: "value",
                    stroke: "#fff",
                    interpolation: {
                      type: "smooth",
                    },
                    marker: {
                      stroke: "#fff",
                      size: 10,
                      fill: "#FD820B",
                      strokeWidth: 2,
                      strokeOpacity: 1,
                    },
                  },
                ],
                background: {
                  fill: "transparent",
                },
                axes: [
                  {
                    position: "left",
                    type: "number",
                    label: {
                      color: "#fff",
                      fontFamily: "yekanBakh",
                    },
                  },
                  {
                    position: "bottom",
                    type: "category",
                    label: {
                      color: "#fff",
                      fontFamily: "yekanBakh",
                    },
                    line: {
                      stroke: "#fff",
                    },
                    crossLines: [
                      {
                        type: "range",
                        fill: "#fff",
                        stroke: "transparent",
                        fillOpacity: 0.25,
                        range: [
                          moment(data?.test?.test_time)
                            .locale("fa")
                            .format("YYYY/MM/DD"),
                          moment(data?.test?.test_time)
                            .locale("fa")
                            .format("YYYY/MM/DD"),
                        ],
                      },
                    ],
                  },
                ],
              }}
            />
          )}
        </div>
      </div>
      <div className="-mt-10 flex justify-center">
        <div className="relative flex w-2/3 flex-col items-center justify-center rounded-2xl bg-base-100 shadow-card">
          <div className="flex w-2/3 justify-center rounded-b-xl bg-primary px-5 py-1">
            <span className="text-xs font-bold text-base-100">
              بازه و محدوده شما
            </span>
          </div>
          <div className="my-2">
            <span className="text-center text-2xl font-bold text-secondary">
              {data?.test?.[params.template]}
            </span>
            {data?.test?.lower && (
              <span className="mb-1 ml-1 mr-1 text-center text-xs text-secondary">
                {data?.template?.unit?.value}
              </span>
            )}
          </div>

          <div className="mb-1">
            {data?.test?.lower ? (
              <div className="flex items-center gap-1.5 text-center text-sm">
                <span>بازه استاندارد:</span>
                <span>از</span>
                <span className="font-bold">{data?.test?.lower}</span>
                <span>تا</span>
                <span className="font-bold">{data?.test?.upper}</span>
              </div>
            ) : (
              <span className="text-center text-sm">
                {data?.template?.unit?.value}
              </span>
            )}
          </div>
        </div>
      </div>

      <div className="mt-6 px-5 text-sm">
        <div>{data?.template?.body}</div>
      </div>
    </div>
  );
}
