import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eft2, CallCalling } from "iconsax-react";
import moment from "jalali-moment";
import PropTypes from "prop-types";
import { useEffect, useMemo } from "react";
import Countdown from "react-countdown";
import { Controller, useForm, useWatch } from "react-hook-form";
import { toast } from "react-hot-toast";
import OTPInput from "react-otp-input";
import { useNavigate } from "react-router-dom";
import { twMerge } from "tailwind-merge";
import loginIcon from "/assets/images/icons/login.png";
import OtpBanner from "/assets/images/otp/banner.png";
import api from "../../api";
import useUserStore from "../../store/userStore.js";
import convertToStandard from "../../utils/convertToStandard.js";
import { getStorage } from "../../utils/utils.js";
import FormField from "../Components/Form/FormField.jsx";
import Button from "../Components/Global/Button.jsx";
import Header from "../Components/Global/Header.jsx";

export default function LoginPage() {
  const form = useForm();
  const {
    mutate: sendOtp,
    data: _data,
    reset: resetSendOtp,
  } = api.Auth.sendOtp.useMutation({
    onError: (error) => {
      toast.error(error?.message);
    },
  });
  const data = useMemo(() => _data?.data, [_data]);

  const { mutate: verifyOtp } = api.Auth.verifyOtp.useMutation({
    onSuccess: (data) => {},

    onError: (error) => {},
  });

  if (data) {
    return (
      <OtpScreen
        reset={resetSendOtp}
        form={form}
        verifyOtp={verifyOtp}
        data={data}
        sendOtp={sendOtp}
      />
    );
  }

  return <MobileScreen form={form} sendOtp={sendOtp} />;
}

function OtpScreen({ form, verifyOtp, sendOtp, reset, data }) {
  const login = useUserStore((state) => state.login);
  const navigate = useNavigate();
  // Removed useAuthToken call

  const mobile = useWatch({
    control: form.control,
    name: "mobile",
  });
  const code = useWatch({
    control: form.control,
    name: "code",
  });

  async function submit(data) {
    verifyOtp(
      {
        ...data,

        mobile,
        link: (await getStorage().getItem("INVITE_CODE")) ?? undefined,
      },
      {
        onSuccess: (res) => {
          if (res.status) {
            login(res.data.user, res.data.token); // Removed setCookie argument
            getStorage().removeItem("INVITE_CODE");
            navigate("/", { replace: true });
          } else {
            toast.error(res.message);
          }
        },
        onError: (error) => {
          toast.error(error?.message);
        },
      },
    );
  }

  const expire_at = useMemo(() => {
    return moment().add(data?.expireAt, "s").valueOf();
  }, [data]);

  useEffect(() => {
    if (code?.length === 4) {
      form.handleSubmit(submit)();
    }
  }, [code]);

  return (
    <section className="relative flex min-h-[100svh] flex-col">
      <Header
        backClassName="mb-7"
        className="aspect-square h-28 bg-pattern bg-cover bg-no-repeat text-base-100"
        back={reset}
      />

      <div className="container absolute top-14 z-20 flex grow flex-col items-center justify-center gap-3">
        <img src={OtpBanner} className="mx-auto" alt="" />
        <h4 className="mt-5 font-bold">تایید شماره موبایل</h4>
        <p className="text-sm text-base-content/80">
          کد ارسالی به شماره {mobile} را وارد نمایید
        </p>

        <div dir="ltr" className="mb-4 mt-3">
          <Controller
            render={({ field: { value, onChange } }) => {
              return (
                <OTPInput
                  value={value}
                  onChange={onChange}
                  numInputs={4}
                  renderSeparator={<span className="w-3" />}
                  inputType={"number"}
                  renderInput={({ className, ...props }) => (
                    <input
                      className={twMerge(
                        "input aspect-square !h-16 !w-16 bg-base-200 pl-0 text-center text-xl font-semibold focus:border-secondary focus:bg-secondary/10 focus:outline-secondary",
                        className,
                      )}
                      {...props}
                    />
                  )}
                />
              );
            }}
            name={"code"}
            control={form.control}
          />
        </div>

        <Countdown
          date={expire_at}
          renderer={({ minutes, seconds, completed }) => {
            return (
              <button
                onClick={() => sendOtp({ mobile })}
                disabled={!completed}
                className="btn relative z-10 h-auto min-h-0 w-full overflow-hidden py-5 disabled:bg-base-300/80 disabled:text-base-content"
              >
                <span
                  className="absolute inset-y-0 right-0 -z-10 inline-block w-[var(--width)] bg-gradient-to-r from-primary to-primary-focus transition-all duration-300"
                  style={{
                    "--width": `${((120 - (minutes * 60 + seconds)) * 100) / 120}%`,
                  }}
                />

                {completed ? (
                  <span>ارسال مجدد کد</span>
                ) : (
                  <span>
                    {minutes.toString().padStart("2", "0")}:
                    {seconds.toString().padStart("2", "0")} پایان اعتبار کد
                  </span>
                )}
              </button>
            );
          }}
        />

        <Button
          onClick={reset}
          variant="ghost"
          className="mt-2 font-medium text-secondary"
        >
          <span>ویرایش شماره موبایل </span>
          <ArrowLeft2 className="h-5 w-5" />
        </Button>
      </div>
    </section>
  );
}

OtpScreen.propTypes = {
  form: PropTypes.object.isRequired,
  verifyOtp: PropTypes.func.isRequired,
  reset: PropTypes.func.isRequired,
  data: PropTypes.object.isRequired,
  sendOtp: PropTypes.func.isRequired,
};

function MobileScreen({ form, sendOtp }) {
  const { isLoading } = sendOtp;

  async function submit(data) {
    sendOtp(
      {
        ...data,
        mobile: convertToStandard(data.mobile),
      },
      {
        onSuccess: (res) => {
          if (!res.status) {
            toast(res.message);
          }
        },
        onError: (error) => {
          toast.error(error?.message);
        },
      },
    );
  }

  return (
    <section className="relative min-h-[100svh]">
      <div className="flex min-h-[550px] flex-col items-center justify-center bg-bg-login bg-cover px-8 text-primary-content" />

      <div className="absolute top-28 z-10 flex h-4/5 w-full items-center justify-center bg-bg-white bg-cover bg-no-repeat">
        <div className="card-body">
          <img className="mb-7 h-16 w-16" src={loginIcon} alt="" />
          <div className="flex items-center gap-3">
            <span className="h-16 rounded-full border-r-4 border-primary" />
            <div className="flex flex-col gap-2">
              <h3 className="text-justify text-2xl font-bold text-base-content">
                {" "}
                ورود | ثبت نام
              </h3>
              <p className="text-center text-sm text-base-content/80">
                شماره موبایل خود را وارد کنید
              </p>
            </div>
          </div>

          <div className="mt-5 space-y-4">
            <FormField
              SuffixIcon={CallCalling}
              form={form}
              name="mobile"
              placeholder="شماره همراه"
              className="rounded-full py-7"
              type="number"
              inputMode="numeric"
              rules={{
                required: "شماره همراه خود را وارد کنید.",
                validate(value) {
                  return value &&
                    !/^09\d{9}$/.test(
                      convertToStandard(value).toString().trim(),
                    )
                    ? "شماره همراه وارد شده نامعتبر است."
                    : true;
                },
              }}
            />

            <Button
              loading={isLoading}
              onClick={form.handleSubmit(submit)}
              variant="primary"
              className="w-full"
            >
              <span className="py-4 text-lg">ورود</span>
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}

MobileScreen.propTypes = {
  form: PropTypes.object.isRequired,
  sendOtp: PropTypes.func.isRequired,
};
