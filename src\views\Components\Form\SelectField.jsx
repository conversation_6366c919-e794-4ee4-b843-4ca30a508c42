import PropTypes from "prop-types";
import { useId } from "react";
import { useWatch } from "react-hook-form";
import { cn } from "../../../utils/utils";

export default function SelectField({
  form,
  Icon,
  label,
  floatingLabel = false,
  name,
  rules,
  options = [],
  ...props
}) {
  const id = useId();

  const field = useWatch({
    control: form.control,
    name,
  });

  return (
    <div className="form-control">
      <div className="relative">
        <select
          value={field || ""}
          {...form.register(name, rules)}
          id={id}
          {...props}
          className={cn(
            "peer select select-bordered h-auto w-full rounded-xl py-2.5 transition-all duration-300 focus:border-secondary focus:outline-secondary",
            {
              "pr-10": !!Icon,
            },
          )}
        >
          <option value="" disabled hidden />
          {options?.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {floatingLabel && (
          <label
            htmlFor={id}
            open={!field}
            className={cn(
              "absolute -top-1 right-1 -translate-y-1/2 bg-base-100 px-2 text-base-content/70 transition-all open:!top-1/2 open:!-translate-y-1/2 peer-focus:-top-1 peer-focus:h-1/4 peer-focus:text-secondary",
              {
                "right-10": !!Icon,
              },
            )}
          >
            {label}
          </label>
        )}
        {Icon && (
          <Icon
            variant="Bold"
            className="absolute right-3.5 top-1/2 h-5 w-5 -translate-y-1/2 text-base-500 peer-focus:text-secondary"
          />
        )}
        {form.formState.errors[name] && (
          <div className="label">
            <span className="label-text-alt">
              {form.formState.errors[name].message}
            </span>
          </div>
        )}
      </div>
    </div>
  );
}

SelectField.propTypes = {
  form: PropTypes.object.isRequired,
  Icon: PropTypes.elementType,
  label: PropTypes.string.isRequired,
  floatingLabel: PropTypes.bool,
  placeholder: PropTypes.string,
  name: PropTypes.string.isRequired,
  rules: PropTypes.object,
  options: PropTypes.array,
};
