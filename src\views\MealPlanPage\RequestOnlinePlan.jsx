import { useMemo, useState } from "react";
import { Loading } from "react-daisyui";
import { useNavigate } from "react-router-dom";
import api from "../../api";
import Header from "../Components/Global/Header";
import IntroStep from "../RequestMealPlanPage/Components/IntroStep";
import OrderFinalizationStep from "../RequestMealPlanPage/OrderFinalizationStep";
import QuestionsLayout from "./../OnlinePlanQuestions/QuestionsLayout";
import DiseasesStep from "./Components/DiseasesStep";
import StartTest from "./Components/StartTest";

const RequestOnlinePlan = () => {
  const navigate = useNavigate();

  const [backCount, setBackCount] = useState(0);

  const { data: _data, isLoading } = api.MealPlans.questions.detail.useQuery({
    variables: { query: { back: backCount } },
  });
  const data = useMemo(() => _data?.data, [_data]);

  const [activeComponent, setActiveComponent] = useState("startTest");

  if (data?.test?.status?.key === "PENDING_ANALYSIS") {
    return <OrderFinalizationStep isOnlinePlan={true} />;
  }

  if (data?.test?.status?.key === "COMPLETE_ANALYSIS") {
    return (
      <IntroStep
        next={() => {
          navigate("/payment");
        }}
      />
    );
  }

  return (
    <div className="flex h-full min-h-dvh flex-col gap-4 bg-base-200 pb-6">
      <Header
        className="sticky top-0"
        back={() => {
          navigate(-1, { replace: true });
        }}
      >
        <span className="font-semibold">درخواست برنامه غذایی</span>
      </Header>
      {isLoading ? (
        <div className="flex w-full flex-1 items-center justify-center">
          <Loading />
        </div>
      ) : data?.questions && data?.questions?.length > 0 ? (
        data?.isFirst && activeComponent === "diseases" ? (
          <DiseasesStep
            defaultPatientsSelected={data?.test?.patients || []}
            setActiveComponent={setActiveComponent}
          />
        ) : (
          <QuestionsLayout
            data={data}
            onBackClick={() => {
              if (data?.isFirst) {
                setActiveComponent("diseases");
              } else {
                setBackCount((prev) => prev + 1);
              }
            }}
            resetBackCount={setBackCount}
          />
        )
      ) : activeComponent === "startTest" ? (
        <StartTest
          onClick={() => {
            setActiveComponent("diseases");
          }}
        />
      ) : (
        <DiseasesStep setActiveComponent={setActiveComponent} />
      )}
    </div>
  );
};

export default RequestOnlinePlan;
